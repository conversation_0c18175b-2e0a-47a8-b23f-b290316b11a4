import { NextRequest, NextResponse } from "next/server";
import fs from 'fs/promises';
import path from 'path';
import { getBaseDir } from '@/utils/fs';

const WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=50630334-3be1-496e-b84d-1d741ec33ea8";

export async function POST(req: NextRequest) {
  try {
    const { filePath, dirType, resolver } = await req.json();

    if (!filePath || !dirType) {
      return NextResponse.json(
        { error: "缺少必要参数" },
        { status: 400 }
      );
    }

    if (!resolver || !resolver.trim()) {
      return NextResponse.json(
        { error: "处理人不能为空" },
        { status: 400 }
      );
    }

    // 构建meta.json文件路径
    const baseDir = getBaseDir(dirType);
    const metaFilePath = path.join(baseDir, filePath, 'meta.json');
    
    // 检查meta.json是否存在
    try {
      await fs.access(metaFilePath);
    } catch (error) {
      return NextResponse.json(
        { error: "meta.json文件不存在" },
        { status: 404 }
      );
    }

    // 读取meta.json内容
    const metaContent = await fs.readFile(metaFilePath, 'utf8');
    const metaData = JSON.parse(metaContent);

    // 检查状态是否为OPEN
    if (metaData.status !== 'OPEN') {
      return NextResponse.json(
        { error: "问题状态不是OPEN，无法解决" },
        { status: 400 }
      );
    }

    // 更新状态为CLOSE
    metaData.status = 'CLOSE';
    metaData.resolvedAt = new Date().toISOString();
    metaData.resolver = resolver.trim();

    // 写回meta.json
    await fs.writeFile(metaFilePath, JSON.stringify(metaData, null, 2), 'utf8');

    // 准备微信群消息内容
    const messageContent = generateWeChatMessage(metaData, filePath);

    // 发送微信群消息
    try {
      const wechatResponse = await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          msgtype: "markdown_v2",
          markdown_v2: {
            content: messageContent
          }
        })
      });

      if (!wechatResponse.ok) {
        console.error('发送微信消息失败:', await wechatResponse.text());
        // 即使微信消息发送失败，也不影响状态更新的成功
      }
    } catch (error) {
      console.error('发送微信消息异常:', error);
      // 即使微信消息发送失败，也不影响状态更新的成功
    }

    return NextResponse.json({ 
      success: true,
      message: "问题已标记为解决",
      status: 'CLOSE'
    });

  } catch (error) {
    console.error("处理问题解决失败:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
}

function generateWeChatMessage(metaData: any, filePath: string): string {
  const {
    tenantName,
    userName,
    submitter,
    email,
    environment,
    createdAt,
    resolvedAt,
    resolver
  } = metaData;

  const createdTime = new Date(createdAt).toLocaleString('zh-CN');
  const resolvedTime = new Date(resolvedAt).toLocaleString('zh-CN');

  return `## 🎉 Bug问题已解决

**问题信息：**
- **租户名称：** ${tenantName || 'N/A'}
- **用户名：** ${userName || 'N/A'}
- **提交人：** ${submitter || 'N/A'}
- **联系邮箱：** ${email || 'N/A'}
- **环境：** ${environment || 'N/A'}

**处理信息：**
- **处理人：** ${resolver || 'N/A'}
- **创建时间：** ${createdTime}
- **解决时间：** ${resolvedTime}

**问题路径：** \`${filePath}\`

---
*此问题已被标记为已解决状态*`;
}
