 
interface FetchDataParams {
  url: string;
  method: string;
  body: any;
  cacheKey?: string;
  processStreamCallback?: (data: any, resultData: any) => void;
  resultDataType?: 'object' | 'array';
  totalMessages?: number;
  cacheCallback?: (cachedResult: any) => void;
  useCache?: boolean;
}
 
export async function fetchData({
  url,
  method,
  body,
  cacheKey,
  processStreamCallback,
  resultDataType = 'object',
  totalMessages = 7,
  cacheCallback,
  useCache = true
}: FetchDataParams): Promise<any> {
  // Check cache first if useCache is true and cacheKey is provided
  if (useCache && cacheKey) {
    const cachedResult = localStorage.getItem(cacheKey);
    if (cachedResult) {
      const cachedResultObject = JSON.parse(cachedResult);
      if (cacheCallback) {
        cacheCallback(cachedResultObject);
      }
      return cachedResultObject;
    }
  }

  try {
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream'
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      throw new Error(`Server error: ${response.status} ${response.statusText}`);
    }

    const reader = response.body!.getReader();
    const decoder = new TextDecoder("utf-8");
    const resultData = resultDataType === 'array' ? [] : {};
    let processedMessages = 0;

    const processStream = async () => {
      let bufferedData = '';
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          if (resultData && Object.keys(resultData).length > 0) {
            localStorage.setItem(cacheKey || '', JSON.stringify(resultData));
          } else {
            throw new Error('Server processing error, please try again!');
          }
          break;
        }

        bufferedData += decoder.decode(value, { stream: true });

        let boundary = bufferedData.indexOf('\n');
        while (boundary !== -1) {
          const chunk = bufferedData.slice(0, boundary);
          bufferedData = bufferedData.slice(boundary + 1);

          if (chunk.startsWith('data: ')) {
            const jsonString = chunk.slice(6).trim();
            if (jsonString === '[DONE]') {
              continue;
            }
            
            try {
              const data = JSON.parse(jsonString);

              if (data.type === 'error') {
                throw new Error(data.message);
              }

              if (data.type === 'heartbeat') {
                continue;
              }

              if (processStreamCallback) {
                processStreamCallback(data, resultData);
              }

              processedMessages++;
            } catch (error) {
              console.error('Error parsing stream data:', error);
            }
          }
          boundary = bufferedData.indexOf('\n');
        }
      }
    };

    await processStream();
    return resultData;
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}

 
export async function uploadFile(file: File, serverUrl: string, tenantInfo: any) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('language', tenantInfo.currentLanguage || 'en');
  formData.append('tenantId', tenantInfo.tenantId || '');
  formData.append('tenantEnv', tenantInfo.tenantEnv || '');
  formData.append('studyNum', tenantInfo.studyNum || '');
  formData.append('userId', tenantInfo.userId || '');
  formData.append('userName', tenantInfo.userName || '');

  const response = await fetch(`${serverUrl}/api/pv/file/upload`, { 
    method: 'POST', 
    body: formData 
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Server error: ${response.status} ${errorText}`);
  }

  return await response.json();
}



export function getTenantInfoFromStorage() {
  const urlParams = new URLSearchParams(window.location.search);
  let tenantName = urlParams.get('tenantName');
  let tenantId = urlParams.get('tenantId');
  let tenantEnv = urlParams.get('tenantEnv');
  let studyNum = urlParams.get('studyNum') || '';
  let userName = urlParams.get('argusUsername') || '';
  let userId = urlParams.get('userId') || '';
 
  if (tenantName && tenantId && tenantEnv) {
    sessionStorage.setItem('tenantName', tenantName);
    sessionStorage.setItem('tenantId', tenantId);
    sessionStorage.setItem('tenantEnv', tenantEnv);
    if (studyNum) sessionStorage.setItem('studyNum', studyNum);
    if (userName) sessionStorage.setItem('userName', userName);
    if (userId) sessionStorage.setItem('userId', userId);

    localStorage.setItem('tenantName', tenantName);
    localStorage.setItem('tenantId', tenantId);
    localStorage.setItem('tenantEnv', tenantEnv);
    if (studyNum) localStorage.setItem('studyNum', studyNum);
    if (userName) localStorage.setItem('userName', userName);
    if (userId) localStorage.setItem('userId', userId);
  } 
  // Otherwise try to get from sessionStorage
  else if (sessionStorage.getItem('tenantName')) {
    tenantName = sessionStorage.getItem('tenantName') || '';
    tenantId = sessionStorage.getItem('tenantId') || '';
    tenantEnv = sessionStorage.getItem('tenantEnv') || '';
    studyNum = sessionStorage.getItem('studyNum') || '';
    userName = sessionStorage.getItem('userName') || '';
    userId = sessionStorage.getItem('userId') || '';

    // Sync to localStorage
    localStorage.setItem('tenantName', tenantName);
    localStorage.setItem('tenantId', tenantId);
    localStorage.setItem('tenantEnv', tenantEnv);
    localStorage.setItem('studyNum', studyNum);
    localStorage.setItem('userName', userName);
    localStorage.setItem('userId', userId);
  }
  // Lastly try localStorage
  else if (localStorage.getItem('tenantName')) {
    tenantName = localStorage.getItem('tenantName') || '';
    tenantId = localStorage.getItem('tenantId') || '';
    tenantEnv = localStorage.getItem('tenantEnv') || '';
    studyNum = localStorage.getItem('studyNum') || '';
    userName = localStorage.getItem('userName') || '';
    userId = localStorage.getItem('userId') || '';

    // Sync to sessionStorage
    sessionStorage.setItem('tenantName', tenantName);
    sessionStorage.setItem('tenantId', tenantId);
    sessionStorage.setItem('tenantEnv', tenantEnv);
    sessionStorage.setItem('studyNum', studyNum);
    sessionStorage.setItem('userName', userName);
    sessionStorage.setItem('userId', userId);
  }
  // Default values if nothing is found
  else {
    tenantName = '-';
    tenantId = '-';
    tenantEnv = '-';
    studyNum = '-';
    userName = '-';
    userId = '-';

    sessionStorage.setItem('tenantName', tenantName);
    sessionStorage.setItem('tenantId', tenantId);
    sessionStorage.setItem('tenantEnv', tenantEnv);
    sessionStorage.setItem('studyNum', studyNum);
    sessionStorage.setItem('userName', userName);
    sessionStorage.setItem('userId', userId);

    localStorage.setItem('tenantName', tenantName);
    localStorage.setItem('tenantId', tenantId);
    localStorage.setItem('tenantEnv', tenantEnv);
    localStorage.setItem('studyNum', studyNum);
    localStorage.setItem('userName', userName);
    localStorage.setItem('userId', userId);
  }

  return { 
    tenantName, 
    tenantId, 
    tenantEnv, 
    studyNum, 
    userName, 
    userId 
  };
}
