import jsonpath from 'jsonpath';

  

export type RULE = {path:string, message:string, validater: (value: any, data: any, path: string, rule: RULE) => boolean | string}
export type ERROR = {path?:string, originalPath:string, message:string, value:any}
export function validate(data: any, rules: RULE[]) {
  const errors: ERROR[] = [];
  
  // 遍历所有规则进行验证
  for (const rule of rules) {
    const { path, message, validater } = rule;
    
    // 使用JSONPath查询匹配的值和路径
    const matches = jsonpath.nodes(data, path);
    
    if (matches.length === 0) {
      // 路径不存在，如果验证不通过，添加错误
      if (!validater(undefined, data, '', rule)) {
        errors.push({
          originalPath: path, 
          message,
          value: undefined
        });
      }
      continue;
    }
    
    // 验证每个匹配的值
    for (const match of matches) { 
      const isValid = validater(match.value, data, match.path.join('.'), rule);
      
      if (!isValid) {
        errors.push({
          path: match.path.join('.'),  
          originalPath: path,  
          message,
          value: match.value
        });
      }
    }
  }
  
  return errors.length > 0 ? errors : null;
}
