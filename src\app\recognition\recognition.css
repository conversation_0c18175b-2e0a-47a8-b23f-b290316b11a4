@import 'tailwindcss';
 
body {
  font-family: '<PERSON><PERSON>', Aria<PERSON>, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  height: 100vh;
  width: 100vw;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-x: hidden;
}

.page-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  width: 100%;
  max-width: 100% !important;
  margin: 0 auto;
}

.main-panel {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 20px;
}

/* Info section styles */
.info-section {
  flex: 1 1 60%;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.info-card {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  flex: 1 1 calc(50% - 20px);
  transition: box-shadow 0.3s, transform 0.3s;
}

.info-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-5px);
}

.info-card h3 {
  margin-bottom: 15px;
  font-size: 20px;
  color: #716DCC;
}

.info-card p {
  margin-bottom: 10px;
  font-size: 16px;
  color: #555;
}

/* 状态样式 */
.status-complete {
  color: var(--status-complete-color);
  font-weight: bold;
}

.status-pending {
  color: var(--status-pending-color);
}

.status-in-progress {
  color: var(--status-in-progress-color);
  font-weight: bold;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Upload section styles */
.upload-section {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  flex: 0 0 300px;
  gap: 20px;
  transition: box-shadow 0.3s, transform 0.3s;
}

.upload-section:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-5px);
}

.upload-section h2 {
  margin-bottom: 20px;
  font-size: 24px;
  color: #716DCC;
}

.dropzone {
  border: 2px dashed #716DCC;
  padding: 40px;
  text-align: center;
  border-radius: 8px;
  background: #f9f9f9;
  transition: border-color 0.3s, background-color 0.3s;
  cursor: pointer;
}

.dropzone:hover, .dropzone-active {
  border-color: #716DCC;
  background-color: #f0f8ff;
}

.small-text {
  font-size: 14px;
  color: #888;
  margin-top: 10px;
}

/* Sidebar styles */
.sidebar {
  position: fixed;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  background-color: #333;
  padding: 10px;
  border-radius: 0 5px 5px 0;
  z-index: 1000; 
}

.sidebar > span { 
  font-size: 24px;
  color: #fff;
  margin-bottom: 10px;
  cursor: pointer;
  transition: color 0.3s;
}

.sidebar i:hover {
  color: #716DCC;
}

/* Loading spinner styles */
.loading-spinner {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 300px;
  height: auto;
  max-height: 70vh;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  text-align: center;
  padding: 20px;
  border-radius: 10px;
  background-color: #716DCC;
  color: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.spinner-icon {
  width: 60px;
  height: 60px;
  border: 8px solid #f3f3f3;
  border-top: 8px solid #716DCC;
  border-radius: 50%;
  margin: 0 auto;
}

.spinner-icon.rotate {
  animation: spin 1s linear infinite;
}

.loading-info {
  margin-top: 20px;
  font-size: 14px;
  line-height: 1.5;
}

#loadingProgress {
  background-color: #716DCC;
}

#currentNode {
  font-weight: bold;
  margin-bottom: 10px;
}

/* Image result container styles */
.image-result-container {
  position: relative;
  display: flex;
  flex-direction: row;
  border: 1px solid #ddd;
  margin-bottom: 10px;
  z-index: 10;
}

.image-panel, .result-panel {
  flex: 1;
  padding: 10px;
}

.image-panel img {
  max-width: 100%;
  height: auto;
}

.result-panel {
  background-color: #fff;
}
 
/* Utility classes */
.hidden {
  display: none;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes flash {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 80%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  line-height: 1;
  color: #999;
  cursor: pointer;
}

.close-button:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.description-content {
  white-space: pre-wrap;
  font-size: 16px;
  line-height: 1.5;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #716DCC;
  color: white;
}

.btn-primary:hover {
  background-color: #5a56a3;
}

.btn-secondary {
  background-color: #e0e0e0;
  color: #333;
}

.btn-secondary:hover {
  background-color: #ccc;
}

.form-control {
  width: 100%;
  padding: 10px;
  font-size: 16px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

/* Media queries for responsive design */
@media (max-width: 768px) {
  .main-panel {
    flex-direction: column;
  }

  .upload-section {
    flex: 1 1 100%;
  }

  .info-section {
    flex: 1 1 100%;
  }
  
  .info-section > div {
    flex: 1 1 100%;
  }
}

/* 配置模态框样式 */
.config-modal {
  max-width: 700px;
}

.config-section {
  margin-bottom: 25px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.config-section:last-child {
  border-bottom: none;
}

.config-section h6 {
  margin-bottom: 15px;
  color: #716DCC;
  font-weight: 600;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  font-size: 16px;
  border: 1px solid #ddd;
  border-radius: 5px;
  transition: border-color 0.3s;
}

.form-control:focus {
  border-color: #716DCC;
  outline: none;
}

button.link-btn {
  background: none;
  border: none;
  color: #716DCC;
  cursor: pointer;
  text-decoration: underline;
  font-size: 16px;
  transition: color 0.3s;
}
