// src/utils/message.ts

const MESSAGE_CONTAINER_ID = '__message_container__';
const DEFAULT_DURATION = 3000; // ms

type MessageType = 'success' | 'error' | 'warning' | 'info';

interface MessageOptions {
  duration?: number;
}

// Ensure the container exists
function getOrCreateContainer(): HTMLElement {
  let container = document.getElementById(MESSAGE_CONTAINER_ID);
  if (!container) {
    container = document.createElement('div');
    container.id = MESSAGE_CONTAINER_ID;
    // Tailwind classes for the container
    container.className = 'fixed top-5 left-1/2 transform -translate-x-1/2 z-[1000] flex flex-col items-center space-y-2';
    document.body.appendChild(container);
  }
  return container;
}

// Function to create and show a single message
function show(text: string, type: MessageType = 'info', options: MessageOptions = {}): void {
  const container = getOrCreateContainer();
  const messageElement = document.createElement('div');

  const { duration = DEFAULT_DURATION } = options;

  // Uniform base classes with white background, border, and shadow
  const baseClasses = 'bg-white border border-gray-300 text-gray-800 px-4 py-2 rounded shadow-lg flex items-center min-w-[200px] max-w-md transition-opacity duration-300 ease-in-out opacity-0';
  let iconSvg = '';

  // Select SVG icon based on type
  // SVGs are simplified representations inspired by Heroicons/FontAwesome
  switch (type) {
    case 'success':
      // Check Circle SVG (Green)
      iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>`;
      break;
    case 'error':
      // X Circle SVG (Red)
      iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" /></svg>`;
      break;
    case 'warning':
       // Exclamation Triangle SVG (Yellow/Amber)
      iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" /></svg>`;
      break;
    case 'info':
    default:
      // Information Circle SVG (Blue)
      iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" /></svg>`;
      break;
  }

  messageElement.className = baseClasses; // Apply uniform classes
  messageElement.innerHTML = `
    <span class="mr-2 flex-shrink-0">${iconSvg}</span>
    <span>${text}</span>
  `;

  container.appendChild(messageElement);

  // Trigger fade-in
  // Use a slight delay to allow the element to be added to the DOM first
  requestAnimationFrame(() => {
    requestAnimationFrame(() => {
       messageElement.classList.remove('opacity-0');
       messageElement.classList.add('opacity-100');
    });
  });


  // Set timeout for removal
  setTimeout(() => {
    // Trigger fade-out
    messageElement.classList.remove('opacity-100');
    messageElement.classList.add('opacity-0');

    // Remove the element after the transition completes
    setTimeout(() => {
      if (messageElement.parentNode === container) {
        container.removeChild(messageElement);
      }
      // Optional: Remove container if empty? Maybe not, keep it for future messages.
      // if (container.childElementCount === 0) {
      //   container.remove();
      // }
    }, 300); // Match the transition duration

  }, duration);
}

// Export an object similar to antd's message API
export const message = {
  success: (text: string, options?: MessageOptions) => show(text, 'success', options),
  error: (text: string, options?: MessageOptions) => show(text, 'error', options),
  warning: (text: string, options?: MessageOptions) => show(text, 'warning', options),
  info: (text: string, options?: MessageOptions) => show(text, 'info', options),
  // You could add a generic 'open' or 'show' method if needed
  // show: show
};