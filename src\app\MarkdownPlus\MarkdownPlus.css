/* 租户初始化页面专用样式 */

.markdown-plus-toc {
  /* 目录滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.markdown-plus-toc::-webkit-scrollbar {
  width: 6px;
}

.markdown-plus-toc::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.markdown-plus-toc::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.markdown-plus-toc::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* 目录项过渡效果 */
.markdown-plus-toc-item {
  transition: all 0.15s ease-in-out;
}

.markdown-plus-toc-item:hover {
  transform: translateX(2px);
}

/* 活跃的目录项样式 */
.markdown-plus-toc-item.active {
  border-left: 3px solid #3182ce;
  background: linear-gradient(90deg, #ebf8ff 0%, transparent 100%);
}

/* 回到顶部按钮动画 */
.markdown-plus-back-to-top {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.markdown-plus-back-to-top:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

/* 标题锚点偏移，避免被固定头部遮挡 */
.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  scroll-margin-top: 6rem;
}

/* 移动端目录按钮样式 */
.markdown-plus-mobile-toggle {
  transition: all 0.2s ease-in-out;
}

.markdown-plus-mobile-toggle:active {
  transform: scale(0.95);
}

/* 图片样式优化 */
.markdown-body img {
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

/* 表格样式优化 */
.markdown-body table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 代码块样式优化 */
.markdown-body pre {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 引用块样式优化 */
.markdown-body blockquote {
  border-radius: 0 8px 8px 0;
  background: linear-gradient(90deg, #f7fafc 0%, transparent 100%);
  margin: 1rem 0;
  padding: 1rem;
}