import { NextRequest, NextResponse } from 'next/server';

// 标准化响应格式
function createStandardResponse(data: any, code: number = 10000, message: string = '') {
  return { data, code, message };
}

// 模拟的租户数据源
const tenantDataSource = [
  { id: 'kelun', name: '科伦博泰', provider: '国荣', database: 'Argus', environment: '验证', accessUrl: 'https://39.103.188.213' },
  { id: 'kelun', name: '科伦博泰', provider: '国荣', database: 'Argus', environment: '正式', accessUrl: 'https://kelunbiopv.clin-nov.com' },
  { id: 'AVB', name: 'Arrivent', provider: '康龙', database: 'Argus', environment: '验证', accessUrl: 'https://argusval.pharmaronclinical.com' },
  { id: 'AVB', name: 'Arrivent', provider: '康龙', database: 'Argus', environment: '正式', accessUrl: 'https://argus.pharmaronclinical.com' },
  { id: 'pms', name: '普米斯', provider: '国荣', database: 'Argus', environment: '验证', accessUrl: 'https://39.101.175.8' },
  { id: 'pms', name: '普米斯', provider: '国荣', database: 'Argus', environment: '正式', accessUrl: 'https://biotheuspv.clin-nov.com' },
  { id: 'ALS', name: '艾力斯', provider: '康龙', database: 'Argus', environment: '验证', accessUrl: 'https://argusval.pharmaronclinical.com' },
  { id: 'ALS', name: '艾力斯', provider: '康龙', database: 'Argus', environment: '正式', accessUrl: 'https://argus.pharmaronclinical.com' },
  { id: 'LaNova', name: '礼新', provider: '康龙', database: 'Argus', environment: '验证', accessUrl: 'https://argusval.pharmaronclinical.com' },
  { id: 'LaNova', name: '礼新', provider: '康龙', database: 'Argus', environment: '正式', accessUrl: 'https://argus.pharmaronclinical.com' },
  { id: 'LaNova', name: '礼新', provider: 'ArisGlobal', database: 'AisG', environment: '验证', accessUrl: 'https://lssmtcval.arisglobal.com.cn' },
  { id: 'LaNova', name: '礼新', provider: 'ArisGlobal', database: 'AisG', environment: '正式', accessUrl: 'https://lssmtcprd.arisglobal.com.cn' },
  { id: 'MIRACOGEN', name: '美亚珂', provider: 'ArisGlobal', database: 'AisG', environment: '正式', accessUrl: 'https://lssmtcprd.arisglobal.com.cn' }
];

// 模拟的临床试验数据源
const trialDataSource = [
  { tenantId: 'kelun', protocolId: 'KL264-01', protocolName: 'SKB264在现有标准治疗难治的不可手术切除的局部晚期或转移性实体瘤患者中的I-II期首次人体试验', indication: '实体瘤', drugNumber: 'CXSL2000010、CXSL2200024' },
  { tenantId: 'kelun', protocolId: 'KL166-III-06', protocolName: '注射用A166对比注射用恩美曲妥珠单抗（T-DM1）在既往接受过曲妥珠单抗和紫杉类治疗的HER2阳性不可切除或转移性乳腺癌患者中的随机、开放、对照、多中心Ⅲ期临床试验', indication: '乳腺癌', drugNumber: 'CXSL1700085' },
  { tenantId: 'kelun', protocolId: 'SKB264-II-04', protocolName: 'SKB264单药或联合治疗晚期或转移性非小细胞肺癌患者的Ⅱ期临床研究', indication: '非小细胞肺癌', drugNumber: 'CXSL2200265，CXSL2200543' },
  { tenantId: 'AVB', protocolId: 'FURMO-002', protocolName: 'A Phase 1b Dose Escalation and Dose Expansion Study Evaluating the Safety, Pharmacokinetics, and Antitumor Activity of Furmonertinib', indication: '非小细胞肺癌', drugNumber: 'N/A' },
  { tenantId: 'AVB', protocolId: 'FURMO-004', protocolName: 'A Global, Phase 3, Randomized, Multicenter, Open-Label Study to Investigate the Efficacy and Safety of Furmonertinib', indication: '肺癌', drugNumber: 'N/A' }
];

// 用户演示数据源
const userDataSource = [
  { id: 1, name: '张三', email: '<EMAIL>', role: 'admin', status: '活跃', createTime: '2024-01-15' },
  { id: 2, name: '李四', email: '<EMAIL>', role: 'user', status: '活跃', createTime: '2024-02-20' },
  { id: 3, name: '王五', email: '<EMAIL>', role: 'user', status: '待激活', createTime: '2024-03-10' },
  { id: 4, name: '赵六', email: '<EMAIL>', role: 'guest', status: '活跃', createTime: '2024-04-05' },
  { id: 5, name: '钱七', email: '<EMAIL>', role: 'user', status: '禁用', createTime: '2024-05-12' },
  { id: 6, name: '孙八', email: '<EMAIL>', role: 'admin', status: '活跃', createTime: '2024-06-01' }
];

// 数据类型到表头的映射
const dataHeaders = {
  tenants: [
    { name: '租户编号', key: 'id' },
    { name: '租户名称', key: 'name' },
    { name: '供应商', key: 'provider' },
    { name: '数据库', key: 'database' },
    { name: '访问环境', key: 'environment' },
    { name: '访问地址', key: 'accessUrl' }
  ],
  trials: [
    { name: '租户编号', key: 'tenantId' },
    { name: '方案编号', key: 'protocolId' },
    { name: '方案名称', key: 'protocolName' },
    { name: '方案适应症', key: 'indication' },
    { name: '药品受理号', key: 'drugNumber' }
  ],
  users: [
    { name: '用户ID', key: 'id' },
    { name: '姓名', key: 'name' },
    { name: '邮箱', key: 'email' },
    { name: '角色', key: 'role' },
    { name: '状态', key: 'status' },
    { name: '创建时间', key: 'createTime' }
  ]
};

export async function GET(request: NextRequest) {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  const { searchParams } = new URL(request.url);
  const type = searchParams.get('type');
  
  // 增强表格参数
  const search = searchParams.get('search') || '';
  const sortBy = searchParams.get('sortBy') || '';
  const page = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('pageSize') || '10');

  try {
    // 获取数据源和表头
    let dataSource: any[] = [];
    let headers: any[] = [];
    
    switch (type) {
      case 'tenants':
        dataSource = tenantDataSource;
        headers = dataHeaders.tenants;
        break;
      case 'trials':
        dataSource = trialDataSource;
        headers = dataHeaders.trials;
        break;
      case 'users':
        dataSource = userDataSource;
        headers = dataHeaders.users;
        break;
      case 'object-array':
        // 返回简单对象数组格式（向后兼容）
        return NextResponse.json(
          createStandardResponse({
            headers: [
              { name: 'ID', key: 'id' },
              { name: '姓名', key: 'name' },
              { name: '年龄', key: 'age' },
              { name: '部门', key: 'department' }
            ],
            rows: [
              { id: 1, name: '张三', age: 25, department: '开发部' },
              { id: 2, name: '李四', age: 30, department: '测试部' },
              { id: 3, name: '王五', age: 28, department: '产品部' }
            ],
            pagination: { total: 3, page: 1, pageSize: 10 },
            sortBy: ''
          })
        );
      default:
        dataSource = tenantDataSource;
        headers = dataHeaders.tenants;
        break;
    }

    // 应用搜索过滤
    let filteredData = [...dataSource];
    if (search) {
      const searchFields = headers.map(h => h.key);
      filteredData = filteredData.filter(item =>
        searchFields.some(field =>
          String(item[field] || '').toLowerCase().includes(search.toLowerCase())
        )
      );
    }

    // 应用排序
    if (sortBy) {
      const [field, direction] = sortBy.split(':');
      if (field && direction) {
        filteredData = [...filteredData].sort((a, b) => {
          const aVal = a[field];
          const bVal = b[field];
          
          // 数字排序
          if (!isNaN(aVal) && !isNaN(bVal)) {
            return direction === 'ASC' ? aVal - bVal : bVal - aVal;
          }
          
          // 字符串排序
          const aStr = String(aVal || '').toLowerCase();
          const bStr = String(bVal || '').toLowerCase();
          
          if (direction === 'ASC') {
            return aStr.localeCompare(bStr);
          } else {
            return bStr.localeCompare(aStr);
          }
        });
      }
    }

    // 应用分页
    const total = filteredData.length;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    // 构建响应数据
    const responseData = {
      headers,
      rows: paginatedData,
      pagination: {
        total,
        page,
        pageSize
      },
      sortBy: sortBy || undefined
    };

    return NextResponse.json(createStandardResponse(responseData));
  } catch (error) {
    return NextResponse.json(
      createStandardResponse(null, 50000, '服务器内部错误'),
      { status: 500 }
    );
  }
}