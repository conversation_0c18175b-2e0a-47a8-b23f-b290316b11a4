import React, { useState, useEffect } from 'react';
import { EnvType, ENV_CONFIG, STORAGE_KEY, fileSystemAPI } from './api';
import { message } from '@/utils/message'; // Import the new message utility

interface EnvSettingModalProps {
  visible: boolean;
  onClose: () => void;
}

const EnvSettingModal: React.FC<EnvSettingModalProps> = ({ visible, onClose }) => {
  const [selectedEnv, setSelectedEnv] = useState<EnvType>('testEnv');

  useEffect(() => { 
    setSelectedEnv(fileSystemAPI.getEnv()); 
  }, []);

  // Updated event type for standard HTML input
  const handleEnvChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedEnv(e.target.value as EnvType);
  };

  const handleSave = () => {
    fileSystemAPI.setEnv(selectedEnv);
    message.success('环境设置已保存');
    onClose(); 
    window.location.reload();
  };

  // Use ternary operator for conditional rendering
  return visible ? (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        {/* Modal Header */}
        <div className="flex justify-between items-center border-b pb-3 mb-4">
          <h3 className="text-xl font-semibold">环境设置</h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-800 text-2xl font-bold">&times;</button>
        </div>
        {/* Modal Body */}
        <div>
          {/* Radio button group replacement */}
          <div className="flex space-x-2 mb-4">
            {(['devEnv', 'testEnv', 'uatEnv'] as EnvType[]).map(env => (
              <label key={env} className={`px-4 py-2 border rounded cursor-pointer ${selectedEnv === env ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'}`}>
                <input
                  type="radio"
                  name="environment"
                  value={env}
                  checked={selectedEnv === env}
                  onChange={handleEnvChange}
                  className="sr-only" // Hide the actual radio button, style the label
                />
                {env === 'devEnv' ? '开发环境' : env === 'testEnv' ? '测试环境' : '预发环境'}
              </label>
            ))}
          </div>
          <div className="mt-5">
            <p className="mb-1">当前选择的环境URL：</p>
            <code className="block bg-gray-100 p-2 rounded text-sm">{ENV_CONFIG[selectedEnv]}</code>
          </div>
        </div>
        {/* Modal Footer */}
        <div className="flex justify-end space-x-3 mt-6 border-t pt-4">
          <button
            onClick={onClose}
            className="px-4 py-2 border rounded text-gray-700 hover:bg-gray-100"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 border rounded bg-blue-500 hover:bg-blue-700 text-white"
          >
            保存
          </button>
        </div>
      </div>
    </div>
  ) : null; // Return null when not visible
};

export default EnvSettingModal;