.fs-container {
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.fs-header {
  margin-bottom: 20px;
}

.fs-header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.fs-header-top h1 {
  margin: 0;
}

.fs-breadcrumb {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.fs-content {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.fs-loading {
  padding: 40px;
  text-align: center;
  font-size: 18px;
  color: #666;
}

.fs-file-table {
  width: 100%;
  border-collapse: collapse;
}

.fs-file-table th,
.fs-file-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.fs-file-table th {
  background-color: #f8f8f8;
  font-weight: 500;
}

.directory {
  cursor: pointer;
  color: #1a73e8;
}

.directory:hover {
  text-decoration: underline;
}
 