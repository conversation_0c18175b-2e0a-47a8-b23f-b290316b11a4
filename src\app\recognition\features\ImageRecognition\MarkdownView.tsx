"use client";
import React from "react";
import { useStateStore } from "../../StateContext";
import { observer } from "mobx-react-lite";
 import Editor from "@/app/fs/ClientEditor"
 import type { Editor as EditorType  } from 'ckeditor5';


type MarkdownViewProps = {
  initialData: string;
  onFocus: (editor: EditorType) => void;
  onChange: (editor: EditorType, content: string) => void;
};

const MarkdownView: React.FC<MarkdownViewProps> = ({
  initialData,
  onFocus,
  onChange,
}) => {
  const appState = useStateStore();
  const { fileStore } = appState;
 

  return (
    <Editor
      data={initialData} 
      onChange={(_event, editor) => {
        const data = editor.getData();
        onChange(editor, data);
      }} 
    />
  );
};

export default observer(MarkdownView);
