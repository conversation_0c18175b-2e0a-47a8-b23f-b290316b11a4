'use client'
import React, { useState } from 'react';
import { useStateStore } from '../../StateContext';
import { observer } from 'mobx-react-lite';
import { message } from '@/utils/message'; // Import message utility

const ExportModal: React.FC = () => {
  const appStore = useStateStore();
  const [exportFormat, setExportFormat] = useState<string>('json');
  const [includeImages, setIncludeImages] = useState<boolean>(true);
  const [includeRecognition, setIncludeRecognition] = useState<boolean>(true);
  const [includeDescription, setIncludeDescription] = useState<boolean>(true);
  const [includeStructuredData, setIncludeStructuredData] = useState<boolean>(true);
  const [includeTransformedData, setIncludeTransformedData] = useState<boolean>(true);
  
  if (!appStore.showExportModal) return null;
  
  // 检查是否有可导出的数据
  const hasDataToExport = appStore.fileStore.pdfMd5 && (
    (includeImages && appStore.fileStore.pdfTotalPage > 0) ||
    (includeRecognition && Array.isArray(appStore.fileStore.recognitionResults) && appStore.fileStore.recognitionResults.length > 0) ||
    (includeDescription && appStore.eventDescription) ||
    (includeStructuredData && Object.keys(appStore.finalStructuredData || {}).length > 0) ||
    (includeTransformedData && Object.keys(appStore.finalTransformedData || {}).length > 0)
  );
  
  // 准备导出数据
  const prepareExportData = () => {
    const exportData: any = {
      pdfMd5: appStore.fileStore.pdfMd5,
      exportTime: new Date().toISOString(),
      tenant: {
        tenantId: appStore.tenantId,
        tenantName: appStore.tenantName,
        tenantEnv: appStore.tenantEnv,
        studyNum: appStore.studyNum
      },
      user: {
        userId: appStore.userId,
        userName: appStore.userName
      }
    };
    
    if (includeRecognition && Array.isArray(appStore.fileStore.recognitionResults) && appStore.fileStore.recognitionResults.length > 0) {
      exportData.recognitionResults = appStore.fileStore.recognitionResults;
    }
    
    if (includeDescription && appStore.eventDescription) {
      exportData.eventDescription = appStore.eventDescription;
    }
    
    if (includeStructuredData && Object.keys(appStore.finalStructuredData || {}).length > 0) {
      exportData.structuredData = appStore.finalStructuredData;
    }
    
    if (includeTransformedData && Object.keys(appStore.finalTransformedData || {}).length > 0) {
      exportData.transformedData = appStore.finalTransformedData;
    }
    
    return exportData;
  };
  
  const handleClose = () => {
    appStore.showExportModal = false;
  }
  // 导出数据
  const handleExport = () => {
    if (!hasDataToExport) {
      // Keep using message.warning
      message.warning('没有可导出的数据');
      return;
    }
    
    const exportData = prepareExportData();
    
    handleClose();

    // 默认导出行为
    let dataStr = '';
    let fileName = '';
    
    // 根据选择的格式处理数据
    if (exportFormat === 'json') {
      dataStr = JSON.stringify(exportData, null, 2);
      fileName = `pv-copilot-export-${appStore.fileStore.pdfMd5.substring(0, 8)}.json`;
    } else if (exportFormat === 'csv') {
      // 简单CSV转换示例
      const csvRows = [];
      
      // 添加表头
      if (includeDescription && appStore.eventDescription) {
        csvRows.push(['Event Description']);
        csvRows.push([appStore.eventDescription.replace(/\n/g, ' ')]);
        csvRows.push([]);  // 空行分隔
      }
      
      // 添加结构化数据
      if (includeStructuredData && appStore.finalStructuredData) {
        csvRows.push(['Structured Data']);
        // 将结构化数据转换为扁平化的键值对
        const flattenObject = (obj: any, prefix = '') => {
          for (const key in obj) {
            if (typeof obj[key] === 'object' && obj[key] !== null) {
              flattenObject(obj[key], `${prefix}${key}.`);
            } else {
              csvRows.push([`${prefix}${key}`, obj[key]]);
            }
          }
        };
        flattenObject(appStore.finalStructuredData);
        csvRows.push([]);  // 空行分隔
      }
      
      // 将数组转换为CSV字符串
      dataStr = csvRows.map(row => row.map(cell => 
        typeof cell === 'string' ? `"${cell.replace(/"/g, '""')}"` : cell
      ).join(',')).join('\n');
      
      fileName = `pv-copilot-export-${appStore.fileStore.pdfMd5.substring(0, 8)}.csv`;
    } else if (exportFormat === 'markdown') {
      // Markdown格式
      const mdContent = [];
      
      mdContent.push('# PV Copilot 导出结果');
      mdContent.push(`**导出时间**: ${new Date().toLocaleString()}`);
      mdContent.push(`**文件MD5**: ${appStore.fileStore.pdfMd5}`);
      mdContent.push('');
      
      if (includeDescription && appStore.eventDescription) {
        mdContent.push('## 事件描述');
        mdContent.push('');
        mdContent.push(appStore.eventDescription);
        mdContent.push('');
      }
      
      if (includeStructuredData && Object.keys(appStore.finalStructuredData || {}).length > 0) {
        mdContent.push('## 结构化数据');
        mdContent.push('');
        mdContent.push('```json');
        mdContent.push(JSON.stringify(appStore.finalStructuredData, null, 2));
        mdContent.push('```');
        mdContent.push('');
      }
      
      if (includeTransformedData && Object.keys(appStore.finalTransformedData || {}).length > 0) {
        mdContent.push('## 转换数据');
        mdContent.push('');
        mdContent.push('```json');
        mdContent.push(JSON.stringify(appStore.finalTransformedData, null, 2));
        mdContent.push('```');
      }
      
      dataStr = mdContent.join('\n');
      fileName = `pv-copilot-export-${appStore.fileStore.pdfMd5.substring(0, 8)}.md`;
    }
    
    // 创建下载链接
    const blob = new Blob([dataStr], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    handleClose();
  };
  
  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">导出数据</h5>
          <button 
            type="button" 
            className="close-button"
            onClick={handleClose}
          >
            ×
          </button>
        </div>
        
        <div className="modal-body">
          <div className="form-section">
            <h6>导出格式</h6>
            <div className="form-group">
              <div className="radio-group">
                <label>
                  <input
                    type="radio"
                    name="exportFormat"
                    value="json"
                    checked={exportFormat === 'json'}
                    onChange={() => setExportFormat('json')}
                  />
                  JSON 格式
                </label>
                
                <label>
                  <input
                    type="radio"
                    name="exportFormat"
                    value="csv"
                    checked={exportFormat === 'csv'}
                    onChange={() => setExportFormat('csv')}
                  />
                  CSV 格式
                </label>
                
                <label>
                  <input
                    type="radio"
                    name="exportFormat"
                    value="markdown"
                    checked={exportFormat === 'markdown'}
                    onChange={() => setExportFormat('markdown')}
                  />
                  Markdown 格式
                </label>
              </div>
            </div>
          </div>
          
          <div className="form-section">
            <h6>导出内容</h6>
            <div className="checkbox-group">
              <label>
                <input
                  type="checkbox"
                  checked={includeRecognition}
                  onChange={() => setIncludeRecognition(!includeRecognition)}
                  disabled={!(Array.isArray(appStore.fileStore.recognitionResults) && appStore.fileStore.recognitionResults.length > 0)}
                />
                图像识别结果
              </label>
              
              <label>
                <input
                  type="checkbox"
                  checked={includeDescription}
                  onChange={() => setIncludeDescription(!includeDescription)}
                  disabled={!appStore.eventDescription}
                />
                事件描述
              </label>
              
              <label>
                <input
                  type="checkbox"
                  checked={includeStructuredData}
                  onChange={() => setIncludeStructuredData(!includeStructuredData)}
                  disabled={!(appStore.finalStructuredData && Object.keys(appStore.finalStructuredData).length > 0)}
                />
                结构化数据
              </label>
              
              <label>
                <input
                  type="checkbox"
                  checked={includeTransformedData}
                  onChange={() => setIncludeTransformedData(!includeTransformedData)}
                  disabled={!(appStore.finalTransformedData && Object.keys(appStore.finalTransformedData).length > 0)}
                />
                转换数据
              </label>
            </div>
          </div>
          
          {!hasDataToExport && (
            <div className="alert alert-warning mt-3">
              没有可导出的数据。请先上传并处理文件。
            </div>
          )}
        </div>
        
        <div className="modal-footer">
          <button 
            type="button" 
            className="btn btn-secondary"
            onClick={handleClose}
          >
            取消
          </button>
          
          <button 
            type="button" 
            className="btn btn-primary"
            onClick={handleExport}
            disabled={!hasDataToExport}
          >
            导出
          </button>
        </div>
      </div>
    </div>
  );
};
 
export default observer(ExportModal);