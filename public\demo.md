# MarkdownPlus 功能演示

这个文档全面演示了 MarkdownPlus 扩展的所有功能，包括 API 调用器、API 表格（含增强功能）和最新的标准化数据结构。

## 目录

1. [API 调用器功能](#api-调用器功能)
2. [基础 API 表格](#基础-api-表格)
3. [API 表格增强功能](#api-表格增强功能)
4. [API 表格按钮功能](#api-表格按钮功能)
5. [综合案例：学生班级管理](#综合案例学生班级管理)
6. [标准化数据结构](#标准化数据结构)
7. [测试和调试](#测试和调试)

---

## API 调用器功能

API 调用器允许在 Markdown 中定义 API 接口并提供交互式调用界面。

### 基本用法

#### GET 请求示例 - 获取测试数据

```api-caller:{
  "id": "get-test-data",
  "name": "获取测试数据",
  "description": "从测试 API 获取不同类型的数据",
  "method": "GET",
  "url": "api/test-data",
  "params": [
    {
      "name": "type",
      "type": "string",
      "required": true,
      "defaultValue": "simple",
      "description": "数据类型：simple, products, stats, large, error, empty"
    },
    {
      "name": "delay",
      "type": "number",
      "required": false,
      "description": "延迟时间（毫秒）"
    }
  ]
}```

#### POST 请求示例 - 提交数据

```api-caller:{
  "id": "post-test-data",
  "name": "提交测试数据",
  "description": "向测试 API 提交数据并获取处理结果",
  "method": "POST",
  "url": "api/test-data",
  "headers": {
    "X-Custom-Header": "test-value"
  },
  "params": [
    {
      "name": "name",
      "type": "string",
      "required": true,
      "defaultValue": "张三",
      "description": "用户姓名"
    },
    {
      "name": "age",
      "type": "number",
      "required": true,
      "defaultValue": 25,
      "description": "用户年龄"
    },
    {
      "name": "gender",
      "type": "enum",
      "required": false,
      "defaultValue": "男",
      "enumOptions": ["男", "女", "其他"],
      "description": "性别选择"
    },
    {
      "name": "active",
      "type": "boolean",
      "required": false,
      "defaultValue": true,
      "description": "是否激活"
    },
    {
      "name": "preferences",
      "type": "json",
      "required": false,
      "defaultValue": {"theme": "dark", "language": "zh-CN"},
      "description": "用户偏好设置（JSON 格式）"
    }
  ]
}```

#### 文件上传示例

```api-caller:{
  "id": "file-upload",
  "name": "文件上传接口",
  "description": "上传文件到服务器",
  "method": "POST",
  "url": "api/fs/upload",
  "params": [
    {
      "name": "files",
      "type": "files",
      "required": true,
      "accept": "image/*,.pdf,.txt,.csv,.xlsx",
      "description": "要上传的文件"
    },
    {
      "name": "category",
      "type": "enum",
      "required": true,
      "defaultValue": "document",
      "enumOptions": ["document", "image", "data", "other"],
      "description": "文件分类"
    },
    {
      "name": "description",
      "type": "string",
      "required": false,
      "description": "文件描述"
    }
  ]
}```

### 支持的参数类型

- **string** - 文本输入框
- **number** - 数字输入框
- **boolean** - 布尔值选择（true/false）
- **enum** - 枚举下拉选择
- **json** - JSON 编辑器
- **file** - 单文件上传
- **files** - 多文件上传

---

## 基础 API 表格

基础 API 表格提供简单的数据展示功能。

### 基本语法

```
```api-table:api/endpoint```
```

### 示例

#### 简单表格展示

```api-table:api/demo-data?type=tenants
```

#### 带自定义样式的表格

```api-table:api/demo-data?type=trials
className: my-6 border-2 border-blue-500 rounded-lg
```

---

## API 表格增强功能

API 表格支持搜索、排序、分页等高级功能，所有操作都通过 API 参数实现。只需要在 `api-table` 语法中添加相应配置即可启用增强功能。

### 基本语法

```
```api-table:api/endpoint```
```

### 功能特性

- **服务端搜索** - 实时搜索，自动重置到第一页
- **列头排序** - 点击表头进行排序（ASC → DESC → 无排序）
- **分页控制** - 可调整每页显示数量，页码导航
- **数据刷新** - 手动刷新数据
- **状态同步** - 前端状态以服务端返回为准

### API 参数格式

配置了增强功能的 API 表格会自动在 API 调用中添加以下参数：

- **搜索**: `search=keyword`
- **排序**: `sortBy=fieldKey:ASC` 或 `sortBy=fieldKey:DESC`
- **分页**: `page=1&pageSize=10`

### 示例

#### 启用增强功能的表格

```api-table:api/demo-data?type=users
```

#### 自定义配置的API表格

```api-table:api/demo-data?type=products
pageSize: 20
showSearch: true
showPagination: true
searchPlaceholder: 搜索产品...
```

### 配置选项

```
```api-table:api/endpoint
pageSize: 20                    # 默认每页显示数量
showSearch: true                # 是否显示搜索框
showPagination: true            # 是否显示分页控件
searchPlaceholder: 搜索...       # 搜索框占位符
className: my-4                 # 自定义CSS类名
```
```

---

## API 表格按钮功能

API 表格支持添加自定义操作按钮，包括表头按钮（全局操作）和行按钮（单行操作）。

### 基本语法

```json
{
  "className": "my-4",
  "buttons": [
    {
      "id": "button-id",
      "text": "按钮文字",
      "description": "按钮描述",
      "type": "header|row",
      "variant": "primary|secondary|danger|success",
      "method": "GET|POST|PUT|DELETE",
      "url": "api/endpoint",
      "rowDataMapping": {
        "paramName": "列名"
      },
      "params": [...]
    }
  ]
}
```

### 示例：用户管理表格

```api-table:api/demo-data?type=users
{
  "className": "my-6",
  "pageSize": 15,
  "buttons": [
    {
      "id": "add-user",
      "text": "添加用户",
      "description": "创建新用户",
      "type": "header",
      "variant": "primary",
      "method": "POST",
      "url": "api/users",
      "params": [
        {
          "name": "name",
          "type": "string",
          "required": true,
          "description": "用户姓名"
        },
        {
          "name": "email",
          "type": "string",
          "required": true,
          "description": "邮箱地址"
        },
        {
          "name": "role",
          "type": "enum",
          "required": true,
          "defaultValue": "user",
          "enumOptions": ["admin", "user", "guest"],
          "description": "用户角色"
        }
      ]
    },
    {
      "id": "edit-user",
      "text": "编辑",
      "description": "编辑用户信息",
      "type": "row",
      "variant": "primary",
      "method": "PUT",
      "url": "api/users",
      "rowDataMapping": {
        "id": "用户ID",
        "currentName": "姓名",
        "currentEmail": "邮箱"
      },
      "params": [
        {
          "name": "id",
          "type": "string",
          "required": true,
          "description": "用户ID"
        },
        {
          "name": "currentName",
          "type": "string",
          "required": false,
          "description": "当前姓名（自动填充）"
        },
        {
          "name": "newName",
          "type": "string",
          "required": false,
          "description": "新姓名"
        },
        {
          "name": "newEmail",
          "type": "string",
          "required": false,
          "description": "新邮箱"
        }
      ]
    },
    {
      "id": "delete-user",
      "text": "删除",
      "description": "删除用户",
      "type": "row",
      "variant": "danger",
      "method": "DELETE",
      "url": "api/users",
      "rowDataMapping": {
        "id": "用户ID",
        "name": "姓名"
      },
      "params": [
        {
          "name": "id",
          "type": "string",
          "required": true,
          "description": "用户ID"
        },
        {
          "name": "name",
          "type": "string",
          "required": false,
          "description": "用户姓名（确认用）"
        },
        {
          "name": "confirm",
          "type": "boolean",
          "required": true,
          "defaultValue": false,
          "description": "确认删除"
        }
      ]
    }
  ]
}
```

### 按钮类型

- **表头按钮** (`type: "header"`) - 显示在表格工具栏，用于全局操作
- **行按钮** (`type: "row"`) - 显示在每行数据的操作列，用于单行操作

### 按钮样式

- **primary** - 主要按钮（蓝色）
- **secondary** - 次要按钮（灰色）
- **danger** - 危险按钮（红色）
- **success** - 成功按钮（绿色）

### 行数据映射

`rowDataMapping` 用于将当前行的数据自动映射到 API 参数：

```json
"rowDataMapping": {
  "paramName": "列名"
}
```

---

## 综合案例：学生班级管理

这是一个完整的学生班级管理系统示例，展示了API表格的所有功能。

### 班级信息管理

```api-table:api/student-class?type=classes
{
  "className": "mb-6",
  "pageSize": 10,
  "buttons": [
    {
      "id": "create-class",
      "text": "创建班级",
      "description": "创建新的班级",
      "type": "header",
      "variant": "primary",
      "method": "POST",
      "url": "api/student-class/classes",
      "params": [
        {
          "name": "name",
          "type": "string",
          "required": true,
          "description": "班级名称"
        },
        {
          "name": "capacity",
          "type": "number",
          "required": true,
          "defaultValue": 30,
          "description": "班级容量"
        },
        {
          "name": "major",
          "type": "string",
          "required": false,
          "description": "专业"
        }
      ]
    },
    {
      "id": "edit-class",
      "text": "编辑",
      "description": "编辑班级信息",
      "type": "row",
      "variant": "primary",
      "method": "PUT",
      "url": "api/student-class/classes",
      "rowDataMapping": {
        "id": "班级ID",
        "currentName": "班级名称",
        "currentCapacity": "容量"
      },
      "params": [
        {
          "name": "id",
          "type": "number",
          "required": true,
          "description": "班级ID"
        },
        {
          "name": "newName",
          "type": "string",
          "required": false,
          "description": "新班级名称"
        },
        {
          "name": "newCapacity",
          "type": "number",
          "required": false,
          "description": "新容量"
        }
      ]
    }
  ]
}
```

### 学生信息管理

```api-table:api/student-class?type=students
{
  "className": "mb-6",
  "pageSize": 20,
  "searchPlaceholder": "搜索学生姓名...",
  "buttons": [
    {
      "id": "add-student",
      "text": "添加学生",
      "description": "添加新学生",
      "type": "header",
      "variant": "primary",
      "method": "POST",
      "url": "api/student-class/students",
      "params": [
        {
          "name": "name",
          "type": "string",
          "required": true,
          "description": "学生姓名"
        },
        {
          "name": "age",
          "type": "number",
          "required": true,
          "description": "年龄"
        },
        {
          "name": "gender",
          "type": "enum",
          "required": true,
          "enumOptions": ["男", "女"],
          "description": "性别"
        },
        {
          "name": "major",
          "type": "string",
          "required": true,
          "description": "专业"
        }
      ]
    },
    {
      "id": "assign-class",
      "text": "分配班级",
      "description": "为学生分配班级",
      "type": "row",
      "variant": "success",
      "method": "POST",
      "url": "api/student-class/assign",
      "rowDataMapping": {
        "studentId": "学生ID",
        "studentName": "姓名"
      },
      "params": [
        {
          "name": "studentId",
          "type": "number",
          "required": true,
          "description": "学生ID"
        },
        {
          "name": "classId",
          "type": "number",
          "required": true,
          "description": "班级ID"
        }
      ]
    }
  ]
}
```

---

## 标准化数据结构

所有 API 现在使用统一的响应格式，确保前后端数据一致性。

### 标准响应格式

```json
{
  "data": {
    "headers": [
      {"name": "用户ID", "key": "id"},
      {"name": "姓名", "key": "name"},
      {"name": "邮箱", "key": "email"}
    ],
    "rows": [
      {"id": "1", "name": "张三", "email": "<EMAIL>"},
      {"id": "2", "name": "李四", "email": "<EMAIL>"}
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "pageSize": 10
    },
    "sortBy": "name:ASC"
  },
  "code": 10000,
  "message": ""
}
```

### 字段说明

#### 数据结构
- **headers** - 表头定义，`name` 用于显示，`key` 用于数据映射和排序
- **rows** - 数据行，对象数组格式，键名对应 `headers` 中的 `key`
- **pagination** - 分页信息
  - `total` - 总记录数
  - `page` - 当前页码
  - `pageSize` - 每页显示数量
- **sortBy** - 当前排序状态，格式为 `fieldKey:ASC` 或 `fieldKey:DESC`

#### 业务状态
- **code** - 业务状态码
  - `10000` - 成功
  - 其他值 - 失败
- **message** - 错误信息（code ≠ 10000 时显示）

### API 调用器响应处理

API 调用器同样支持标准响应格式：

```json
{
  "data": {
    "userId": "12345",
    "userName": "张三",
    "created": true
  },
  "code": 10000,
  "message": ""
}
```

- 成功时显示 `data` 字段内容
- 失败时显示 `message` 错误信息
- 保持向后兼容，支持非标准格式

---

## 测试和调试

### 测试不同数据类型

#### 简单测试数据

```api-table:api/test-data?type=simple
pageSize: 5
```

#### 大数据量测试

```api-table:api/test-data?type=large
pageSize: 10
searchPlaceholder: 搜索大数据...
```

#### 延迟加载测试

```api-table:api/test-data?type=products&delay=2000
pageSize: 8
```

### 错误处理测试

#### 服务器错误

```api-table:api/test-data?type=error
```

#### 空数据

```api-table:api/test-data?type=empty
```

#### 不存在的端点

```api-table:api/non-existent-endpoint
```

### API 调用器测试

#### 错误响应测试

```api-caller:{
  "id": "error-test",
  "name": "错误响应测试",
  "description": "测试API错误响应处理",
  "method": "POST",
  "url": "api/test-error",
  "params": [
    {
      "name": "errorType",
      "type": "enum",
      "required": true,
      "defaultValue": "validation",
      "enumOptions": ["validation", "server", "timeout", "auth"],
      "description": "错误类型"
    }
  ]
}```

---

## 功能对比

| 功能 | 基础表格 | API表格 | 表格+按钮 |
|------|----------|----------|-----------|
| 数据展示 | ✅ | ✅ | ✅ |
| 自动加载 | ✅ | ✅ | ✅ |
| 错误处理 | ✅ | ✅ | ✅ |
| 自定义样式 | ✅ | ✅ | ✅ |
| 搜索功能 | ❌ | ✅ | ✅ |
| 排序功能 | ❌ | ✅ | ✅ |
| 分页功能 | ❌ | ✅ | ✅ |
| 操作按钮 | ❌ | ❌ | ✅ |
| 行数据映射 | ❌ | ❌ | ✅ |
| API 调用历史 | ❌ | ❌ | ✅ |

## 最佳实践

### 1. 选择合适的组件

- **简单数据展示** → 使用基础 `api-table`
- **需要搜索/排序/分页** → 使用 `api-table` 并配置相应参数
- **需要数据操作** → 使用带按钮的API表格
- **API 测试和调试** → 使用 `api-caller`

### 2. 性能优化

- 大数据量使用服务端分页
- 合理设置 `pageSize`（建议 10-50）
- 使用防抖搜索（组件已内置）

### 3. 用户体验

- 提供清晰的按钮文字和描述
- 使用合适的按钮样式表示操作类型
- 重要操作添加确认参数

### 4. 错误处理

- 后端返回标准错误格式
- 前端显示友好的错误信息
- 提供重试机制

---

## 技术规范

### 语法格式

1. **API 调用器**：
   ```
   ```api-caller:{JSON配置}```
   ```

2. **基础表格**：
   ```
   ```api-table:URL```
   ```

3. **API表格**：
   ```
   ```api-table:URL```
   ```

4. **带配置的表格**：
   ```
   ```api-table:URL
   配置项: 值
   ```
   ```

### URL 参数规范

- **搜索**: `search=keyword`
- **排序**: `sortBy=field:ASC` 或 `sortBy=field:DESC`
- **分页**: `page=1&pageSize=10`
- **自定义参数**: 根据业务需求添加

### 响应规范

所有 API 响应必须遵循标准格式，确保前端组件正确解析和显示数据。

---

这个演示文档展示了 MarkdownPlus 的完整功能集，包括最新的标准化数据结构和增强功能。你可以根据实际需求选择合适的组件和配置。