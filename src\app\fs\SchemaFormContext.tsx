import { createContext, useContext } from "react";

export interface SchemaFormProps {
  data: any;
  onChange?: (data: any, path?: string, value?: any) => void;
  readonly: boolean;
}

export const SchemaFormContext = createContext<SchemaFormProps | null>(null);

export const useSchemaForm = () => {
  const context = useContext(SchemaFormContext);
  if (!context) {
    throw new Error('useSchemaForm must be used within a SchemaFormProvider');
  }
  return context;
};
