import { NextRequest, NextResponse } from "next/server";
import { deleteFile, DIR } from "@/utils/fs";

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, filePath, password } = body;

    // 验证必填字段
    if (!filePath) {
      return NextResponse.json({ error: "文件路径不能为空" }, { status: 400 });
    }

    if (!password) {
      return NextResponse.json({ error: "密码不能为空" }, { status: 400 });
    }

    // 简单的密码验证 (在实际项目中应该使用更安全的验证方式)
    const adminPassword = process.env.ADMIN_PASSWORD || "admin123";
    if (password !== adminPassword) {
      return NextResponse.json({ error: "密码错误" }, { status: 401 });
    }

    const dirType = (type || "document") as DIR;
    const result = await deleteFile(dirType, filePath);

    return NextResponse.json(result);
  } catch (error) {
    console.error("文件删除失败:", error);
    return NextResponse.json(
      { error: (error as Error).message || '文件删除失败' },
      { status: 400 }
    );
  }
}
