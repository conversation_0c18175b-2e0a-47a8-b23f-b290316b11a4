import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // 模拟PMC表名数据
    const tables = [
      'user_table',
      'product_table', 
      'order_table',
      'payment_table',
      'inventory_table',
      'customer_table',
      'supplier_table',
      'category_table',
      'report_table',
      'log_table'
    ];

    return NextResponse.json({
      data: tables
    });
  } catch (error) {
    console.error('Error fetching PMC tables:', error);
    return NextResponse.json(
      { error: 'Failed to fetch PMC tables' },
      { status: 500 }
    );
  }
}
