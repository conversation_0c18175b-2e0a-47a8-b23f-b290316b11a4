import React, { useCallback, useEffect, useState } from 'react';
import { FaDownload, FaExternalLinkAlt, FaWindowClose, FaPrint } from 'react-icons/fa'; 
import dynamic from 'next/dynamic';
import Image from 'next/image';
import MarkdownRenderer from './MarkdownRenderer';
import { SchemaForm } from './SchemaForm'
const ReactJsonView = dynamic(() => import('@microlink/react-json-view'), {
  ssr: false,
});

import type { DIR } from '@/utils/fs'; // Import DIR type

interface FilePreviewModalProps {
  onClose: () => void;
  fileUrl: string;
  fileName: string;
  fileType: string;
  dirType: DIR; // Add dirType prop
  onDownload?: () => void;
}

const FilePreviewModal: React.FC<FilePreviewModalProps> = ({
  onClose,
  fileUrl,
  fileName,
  fileType,
  dirType, // Destructure dirType
  onDownload
}) => {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const loadFileContent = useCallback(async () => {
    if (!fileUrl) return;
    setLoading(true);
    try {
      const response = await fetch(fileUrl);
      const data = await response.text();
      setContent(data);
    } catch (error) {
      console.error('加载文件内容失败:', error);
    } finally {
      setLoading(false);
    }
  }, [fileUrl]);

  const onPrint = () => {
    window.print();
  };

  useEffect(() => {
    if (fileUrl && ['txt','md','json'].includes(fileType)) {
      loadFileContent();
    }
  }, [fileUrl,fileType,loadFileContent]);
 

  const handleOpenInNewWindow = () => {
    window.open(fileUrl, '_blank');
  };

  const renderContent = () => {
    if (loading) {
      return <div className="text-center p-8">加载中...</div>; // Replaced Spin with text
    }

    if(fileName === 'extract_result.json' && !!content){  
      return <SchemaForm readonly data={JSON.parse(content)} />;
    }

    switch (fileType) {
      case 'txt':
      case 'md': 
        const urlParams = new URLSearchParams(location.search);
        const filePath = urlParams.get('path') || ''; 
 
        return <MarkdownRenderer
                  content={content}
                  fileName={fileName}
                  filePath={filePath}
                  dirType={dirType}
               />;
      case 'json':
        try {
          const jsonData = JSON.parse(content); 
          return (
            <ReactJsonView 
              src={jsonData}
            /> 
          );
        } catch {
          return <div>Invalid JSON content</div>;
        }
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <img 
              src={fileUrl}
              alt={fileName}
              className="max-w-full mx-auto"
            />
          </div>
        );
      case 'pdf':
        return (
          <iframe
            src={`${fileUrl}#toolbar=0`}
            style={{
              width: '100%',
              height: 'calc(100vh - 120px)',
              border: 'none'
            }}
          />
        );
      default:
        return <div>不支持预览此类型的文件</div>;
    }
  };
  

  return (
    <div className="modal-container-print-reset fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-0 m-0">
      <div className="bg-white rounded-lg shadow-xl w-full h-full flex flex-col">
        {/* Modal Header */}
        <div className="print:hidden flex justify-between items-center p-2 border-b">
          <span className="font-semibold text-lg">预览: {fileName}</span>
          <div className="flex items-center space-x-2">
            <button
               title="打印"
               className="px-3 py-1 border rounded cursor-pointer bg-blue-500 hover:bg-blue-700 text-white text-sm flex items-center space-x-1"
               onClick={onPrint}
             >
               <FaPrint />
               <span>打印</span>
             </button>
             
             <button
               title="下载"
               className="px-3 py-1 border rounded cursor-pointer bg-blue-500 hover:bg-blue-700 text-white text-sm flex items-center space-x-1"
               onClick={onDownload}
             >
               <FaDownload />
               <span>下载</span>
             </button>
             <button
               title="新窗口打开"
               className="px-3 py-1 border cursor-pointer rounded bg-gray-500 hover:bg-gray-700 text-white text-sm flex items-center space-x-1"
               onClick={handleOpenInNewWindow}
             >
               <FaExternalLinkAlt />
               <span>新窗口打开</span>
             </button>
             <button
               title="关闭"
               className="px-3 py-1 cursor-pointer border rounded bg-gray-500 hover:bg-gray-700 text-white text-sm flex items-center space-x-1"
               onClick={onClose}
             >
               <FaWindowClose />
               <span>关闭</span>
             </button> 
          </div>
        </div>
        {/* Modal Body */}
        <div className="printable-content flex-grow overflow-auto p-1">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default FilePreviewModal;