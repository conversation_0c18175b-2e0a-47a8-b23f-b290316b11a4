'use client'
import { useState, useEffect } from 'react';
import { useStateStore } from '../../StateContext';
import { observer } from 'mobx-react-lite';
 
const EventDescriptionModal: React.FC = () => {
  const appStore = useStateStore();
  const [description, setDescription] = useState(appStore.eventDescription || '');
  const [isEditing, setIsEditing] = useState(false);
   
  // 切换编辑/查看模式
  const toggleEditMode = () => {
    setIsEditing(!isEditing);
  };
  
  // 处理保存按钮点击
  const handleSave = () => { 
    appStore.eventDescription = description;  
    
    const cacheKey = `${appStore.fileStore.pdfMd5}_eventDescription`;
    localStorage.setItem(cacheKey, JSON.stringify({ description }));
    setIsEditing(false);
  };
  
  // 处理重新生成按钮点击
  const handleRegenerate = () => {
    appStore.regenerateEventDescription();
    handleClose();
  };

  const handleClose = () => {
    appStore.setEventDescriptionModal(false);
  };
  
  if (!appStore.showEventDescriptionModal) return null;
  
  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">事件描述</h5>
          <button 
            type="button" 
            className="close-button"
            onClick={handleClose}
          >
            ×
          </button>
        </div>
        
        <div className="modal-body">
          {isEditing ? (
            <textarea
              className="form-control"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={20}
            />
          ) : (
            <div 
              className="description-content"
              dangerouslySetInnerHTML={{ __html: description }}
            />
          )}
        </div>
        
        <div className="modal-footer">
          <button 
            type="button" 
            className="btn btn-secondary"
            onClick={handleClose}
          >
            关闭
          </button>
          
          <button 
            type="button" 
            className="btn btn-primary"
            onClick={isEditing ? handleSave : toggleEditMode}
          >
            {isEditing ? '保存' : '更新'}
          </button>
          
          <button 
            type="button" 
            className="btn btn-primary"
            onClick={handleRegenerate}
          >
            重新生成
          </button>
        </div>
      </div>
    </div>
  );
};

 

export default observer(EventDescriptionModal);