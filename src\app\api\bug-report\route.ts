import { NextRequest, NextResponse } from "next/server";
import fs from 'fs/promises';
import path from 'path'
import dayjs from 'dayjs'
import shell from 'shelljs'
import crypto from 'crypto'
const REPORT_BASE_DIR = process.env.REPORTBUG_BASE_DIR || './reports'; 
const DOCUMENTS_DIR = process.env.DOCUMENTS_BASE_DIR || './documents'; 

async function mkdir(path: string){
  try {
    await fs.mkdir(path, { recursive: true });
  } catch (error) {
    console.error(`创建目录失败: ${path}`, error);
  }
}

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    
    // 获取表单数据
    const environment = formData.get("environment") as string;
    const currentLanguage = formData.get("currentLanguage") as string;
    const tenantId = formData.get("tenantId") as string;
    const tenantName = formData.get("tenantName") as string;
    const studyNum = formData.get("studyNum") as string;
    const userId = formData.get("userId") as string;
    const userName = formData.get("userName") as string;
    const pdfMd5 = formData.get("pdfMd5") as string;
    const pdfName = formData.get("pdfName") as string;
    const content = formData.get("content") as string;
    const email = formData.get("email") as string; // 新增邮箱字段
    const submitter = formData.get("submitter") as string; // 新增提交人字段

    // 验证必填字段
    if (!tenantId || !userId || !studyNum || !pdfMd5 || !content) {
      return NextResponse.json(
        { error: "缺少必要参数" },
        { status: 400 }
      );
    }
    const dateStr = dayjs().format('YYYYMMDDHHmmss');
    const dir = path.join(REPORT_BASE_DIR, tenantId, studyNum, pdfMd5, dateStr);
    await mkdir(dir);

    shell.cp('-R', path.join(DOCUMENTS_DIR, pdfMd5), dir);
  
    const bugReport = {
      environment,
      currentLanguage,
      tenantName,
      userName,
      tenantId,
      userId,
      fileName: pdfName || null,
      email: email || null, // 添加邮箱字段到meta.json
      submitter: submitter || null, // 添加提交人字段到meta.json
      status: 'OPEN', //OPEN CLOSE 
      createdAt: new Date().toISOString()
    };

    await fs.writeFile( path.join(dir,'meta.json'), JSON.stringify(bugReport),'utf8'); 

    if(content){
      await fs.writeFile(path.join(dir,'report.md'), content,'utf8');
    }
 

    // 示例响应
    return NextResponse.json({ 
      success: true,
      message: "Bug反馈提交成功",
      path: [tenantId, studyNum, pdfMd5, dateStr].join('/')
    });

  } catch (error) {
    console.error("处理Bug反馈失败:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
}