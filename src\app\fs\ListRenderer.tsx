import React from "react";
import { ValueRenderer } from "./ValueRenderer";

interface ListRendererProps {
  path: string;
  data: any[];
}

export const ListRenderer: React.FC<ListRendererProps> = ({ path, data }) => {
  if (data.length === 0) {
    return <div className="text-gray-400">No data</div>;
  }

  return (
    <div data-path={`${path}`} className="flex flex-wrap gap-2">
      {data.map((item, index) => (
        <div data-path={`${path}${index}`} key={index} className="px-3 py-1 bg-blue-50 rounded-full">
          <ValueRenderer path={`${path}${index}.`} value={item} />
        </div>
      ))}
    </div>
  );
};
