'use client';
import { makeAutoObservable, runInAction } from "mobx";
import type { Editor } from 'ckeditor5';
import { getFingerprint, setOption } from '@thumbmarkjs/thumbmarkjs'
import { getTenantInfoFromStorage, fetchData } from '../api'; 
import type { AppSettings } from "../types";
import FileStore from "./FileStore";


setOption('exclude', ['canvas', 'permissions', 'screen.mediaMatches', 'plugins', 'system.useragent', 'system.browser.name', 'system.browser.version', 'hardware.videocard.vendorUnmasked', 'hardware.videocard.rendererUnmasked', 'fonts'])


class StateStore {
  fileStore: FileStore;

  basePath = "";
  settings: AppSettings = {
    currentLanguage: "en",
    environment: "uat",
    operationMode: "auto",
    devEnv: "http://localhost:2189",
    testEnv: "https://copilot-test.pharmaronclinical.com",
    uatEnv: "https://copilot-uat.pharmaronclinical.com",
    renderFormat: "markdown",
    reportType: "initial",
  };
  lastSwitchTime = 0; // 上次切换语言的时间
  switchInterval = 5000; // 语言切换的时间间隔限制 
  eventDescription = ""; // 新增，用于存储事件描述 
  isRecognitionModified = false; // 新增属性
  isStructuredDataModified = false; // 新增，用于标记结构化数据是否被修改
  isTransformedDataModified = false; // 新增，用于标记二次转换数据是否被修改
  insightData = {};
  finalStructuredData = {};
  finalTransformedData = {};    
  tenantId = '';
  tenantName = '';
  studyNum = ''; 
  tenantEnv = ''; 
  userId = '';
  userName = '';
  startTime = '';
  endTime = '';
  loading = {
    show:false,
    name: '',
    progress: 0,
    error: '',
  };
  
  showEventDescriptionModal = false;
  showConfigModal = false;
  showExportModal = false;

  promptsData = {};
  modifiedPrompts = new Set(); // 新增，用于记录被修改的提示词
  lastActiveEditor: Editor | null = null; // 新增，用于记录最后一个活动的编辑器实例
  lastActiveTime = 0;
  isDataReviewModified = false; // 标记数据复核的数据是否被修改
  fieldMapping = {}; // 字段映射关系
  reverseFieldMapping = {}; // 逆向字段映射关系
  dataReviewData = {}; // 数据复核的数据
  allPrompts:  { 'cn': any, 'en': any } = { cn: {}, en: {}};
  tenantList = [
    { tenantId: "kelun", tenantName: "科伦" },
    { tenantId: "ALS", tenantName: "艾力斯" },
    { tenantId: "DEFAULT", tenantName: "普米斯" },
    { tenantId: "LaNova", tenantName: "礼新" },
    { tenantId: "inxmed", tenantName: "应世" },
    { tenantId: "miracogen", tenantName: "美雅珂" },
    { tenantId: "hspharm", tenantName: "翰森" },
    { tenantId: "tykmedicines", tenantName: "同源康" },
  ];

  modulesPlaceholders = [
    {
      module: "studyInfo",
      placeholder: "studyData",
    },
    {
      module: "dictInfo",
      placeholder: [
        "{desc_reptd}",
        "{ind_reptd}",
        "{labtest}",
        "{pat_exposure}",
        "{pat_hist_rptd}",
        "{product_name}",
        "{TXT_cdr_admin_route_id}",
        "{TXT_cdr_dose_unit_id}",
        "{TXT_formulation_id}",
        "{TXT_freq_id}",
        "{TXT_labunit}",
      ],
    },
    {
      module: "encodeCache",
      placeholder: [
        "desc_reptd",
        "Ind_Table_ind_reptd",
        "labtest",
        "pat_exposure",
        "product_name",
        "rel_hist_table_pat_hist_rptd",
        "TXT_cdr_admin_route_id",
        "TXT_cdr_dose_unit_id",
        "TXT_formulation_id",
        "TXT_freq_id",
        "TXT_labunit_0",
      ],
    },
  ];

  i18n = {
    module: {
      studyInfo: "临床试验",
      dictInfo: "字典配置",
      encodeCache: "字典缓存",
    },
    placeholder: {
      studyData: "方案信息",
      desc_reptd: "Meddra编码-不良事件",
      Ind_Table_ind_reptd: "Meddra编码-药品适应症",
      labtest: "Meddra编码-实验室检查",
      pat_exposure: "药品-试验用药品编码",
      product_name: "whoDrug编码",
      rel_hist_table_pat_hist_rptd: "Meddra编码-相关病史",
      TXT_cdr_admin_route_id: "药品-给药途径",
      TXT_cdr_dose_unit_id: "药品-剂量单位",
      TXT_formulation_id: "药品-给药剂型",
      TXT_freq_id: "药品-给药频率",
      TXT_labunit_0: "实验室检查-检查结果单位",
    },
  };

  fieldData = [
    {
      key: "方案编号",
      value: "",
      type: "readonly",
      path: "datetime.protocal_number",
    },
    {
      key: "SAE报告术语",
      value: [],
      type: "readonly",
      path: "datetime.desc_reptd",
    },
    {
      key: "SAE发生日期(最早)",
      value: "",
      type: "required",
      path: "datetime.onset",
    },
    {
      key: "试验用药给药日期(最早)",
      value: "",
      type: "required",
      path: "datetime.start_datetime",
    },
    {
      key: "收到报告日期",
      value: "",
      type: "optional",
      path: "datetime.report_receive_date",
    },
    {
      key: "受试者入院日期",
      value: "",
      type: "optional",
      path: "datetime.admission_date",
    },
  ];

  // 保存提示词数据
  async savePromptsData(promptsData: { 'cn': any, 'en': any }) {
    try {
      for (const language in promptsData) {
        const languagePrompts = (promptsData as any)[language];
        for (const templateKey in languagePrompts) {
          const content = languagePrompts[templateKey];
          const response = await fetch(`${this.basePath}/api/pv/prompt/${this.tenantId}/${templateKey}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              content,
              language,
              tenantId: this.tenantId,
              tenantEnv: this.tenantEnv,
              studyNum: this.studyNum,
              userId: this.userId,
              userName: this.userName,
            }),
          });
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Failed to save prompt ${templateKey}: ${errorData.detail || response.statusText}`);
          }
        }
      }
      console.log('Prompts saved successfully');
      // 更新已修改的提示词的初始值
      for (const promptKey in this.allPrompts) {
        const promptData = (this.allPrompts as any )[promptKey];
        if (this.modifiedPrompts.has(`${promptKey}_cn`)) {
          promptData.initialCnValue = promptData.cn;
        }
        if (this.modifiedPrompts.has(`${promptKey}_en`)) {
          promptData.initialEnValue = promptData.en;
        }
      }
      // 清空修改过的提示词集合
      this.modifiedPrompts.clear();
    } catch (error) {
      console.error('Error saving prompts data:', error);
      throw error;
    }
  }

  // 更新操作模式配置
  updateOperationMode(operationMode: AppSettings['operationMode'], renderFormat: AppSettings['renderFormat'], reportType: AppSettings['reportType']) {
    this.settings.operationMode = operationMode;
    this.settings.renderFormat = renderFormat;
    this.settings.reportType = reportType;
    localStorage.setItem('appState', JSON.stringify(this.settings));
  }

  // 切换语言
  async switchLanguage() {
    const now = Date.now();
    if (now - this.lastSwitchTime < this.switchInterval) {
      return;
    }
    this.lastSwitchTime = now;
    this.settings.currentLanguage = this.settings.currentLanguage === 'en' ? 'cn' : 'en';
    localStorage.setItem('appState', JSON.stringify(this.settings));
  }

  // 处理文件移除
  handleFileRemoved(file: File) {
    //  TODO
  }
  

  constructor() {
    makeAutoObservable(this);
    this.fileStore = new FileStore(this);
    // this.init();
  }
  async init() {
    if (Notification.permission !== 'granted' && Notification.permission !== 'denied') {
      Notification.requestPermission();
    } 
    
    const tenantInfo = getTenantInfoFromStorage(); 
    this.tenantId = tenantInfo.tenantId;
    this.tenantName = tenantInfo.tenantName;
    this.tenantEnv = tenantInfo.tenantEnv;
    this.studyNum = tenantInfo.studyNum;
    this.userId = tenantInfo.userId;
    this.userName = tenantInfo.userName;  
    
    if(this.userId === '-'){
      const fp = await getFingerprint(true);
      this.userId = fp.hash; 
    }

    const _appState = JSON.parse(localStorage.getItem("appState") || "{}");
    this.settings.currentLanguage = _appState.currentLanguage || "en";
    this.settings.environment = _appState.environment || "uat";
    this.settings.operationMode = _appState.operationMode || "auto";    
    this.settings.renderFormat = _appState.renderFormat || "markdown";  
    this.settings.reportType = _appState.reportType || "initial";
    this.basePath = _appState.environment === "dev" ? _appState.devEnv : _appState.environment === "test" ? _appState.testEnv : _appState.uatEnv;

  }  
  showLoading(name: string,  progress?: number , error?: string){
    this.loading.show = true;
    this.loading.name = name;
    if (progress) {
      this.loading.progress = progress;
    }
    if (error) {
      this.loading.error = error;
    }
  }
  hideLoading(){
    this.loading.show = false;
    this.loading.name = '';
    this.loading.progress = 0;
    this.loading.error = '';
  }
  updateSettings(settings: AppSettings){
    this.settings = {...settings}; 
    let basePath = '';
    switch (settings.environment) {
      case 'dev':
        basePath = settings.devEnv;
        break;
      case 'test':
        basePath = settings.testEnv;
        break;
      case 'uat':
        basePath = settings.uatEnv;
        break;
    }
    
    if (basePath) {
      this.basePath = basePath;
    } 

    localStorage.setItem("appState", JSON.stringify(settings));
    localStorage.setItem("tenantId", this.tenantId);
    localStorage.setItem("tenantName", this.tenantId);

  }
  updateTenant(id: string){ 
     const selectedTenantInfo = this.tenantList.find(
      (tenant ) => tenant.tenantId === id
    );
    
    if (selectedTenantInfo) { 
      sessionStorage.setItem('tenantId', selectedTenantInfo.tenantId);
      sessionStorage.setItem('tenantName', selectedTenantInfo.tenantName);
      localStorage.setItem('tenantId', selectedTenantInfo.tenantId);
      localStorage.setItem('tenantName', selectedTenantInfo.tenantName);
      
      this.tenantId = selectedTenantInfo.tenantId;
      this.tenantName = selectedTenantInfo.tenantName; 
    }

  } 
  regenerateEventDescription(){
    // TODO
  }
  setEventDescriptionModal(show: boolean){
    this.showEventDescriptionModal = show;
  }
  setConfigModal(show: boolean){
    this.showConfigModal = show;
  }
  setExportModal(show: boolean){
    this.showExportModal = show;
  } 
  isAutoMode(){
    return this.settings.operationMode === 'auto';
  }
}

export default StateStore;
  