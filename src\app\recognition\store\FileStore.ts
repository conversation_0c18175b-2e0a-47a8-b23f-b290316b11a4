import { makeObservable, makeAutoObservable, runInAction } from "mobx";
import AppStateStore from "./StateStore";
import { uploadFile, fetchData } from "../api";
import { showNotification } from "@/utils/notification";
import { message } from "@/utils/message"; // Import the new message utility
/**
 * FileStore - 负责管理文件上传和处理的MobX Store
 */
export class FileStore {
  // 存储上传的文件列表
  file: File | undefined = undefined;
  fileName = "";
  base64Images: string[] = [];
  pdfMd5 = "";
  pdfTotalPage = 0;
  recognitionResults: { content: string }[] = [];

  appStore: AppStateStore;

  constructor(appStore: AppStateStore) {
    this.appStore = appStore;
    makeAutoObservable(this);
  }

  getRequestBody() {
    return {
      md5: this.pdfMd5,
      tenantId: this.appStore.tenantId,
      tenantEnv: this.appStore.tenantEnv,
      studyNum: this.appStore.studyNum,
      userId: this.appStore.userId,
      userName: this.appStore.userName,
    };
  }

  removeFile() {
    this.file = undefined;
    this.fileName = "";
    this.base64Images = [];
    this.pdfMd5 = "";
    this.pdfTotalPage = 0;
  }

  async uploadFile(file: File) {
    this.file = file;
    this.fileName = file.name;
    if (file.type === "application/pdf") {
      // 处理PDF文件上传
      await this.handlePdfUpload();
    } else {
      // 处理图片文件上传
      await this.handleImageUpload();
    }

    await this.processImageRecognition();
  }

  async processImageRecognition() {
    this.appStore.showLoading("正在处理图像识别");

    const url = `${this.appStore.basePath}/api/pv/recognize/stream/chat`;
    const body = this.getRequestBody();
    const cacheKey = `${this.pdfMd5}_imageRecognition`;

    try {
      const recognitionResults = await fetchData({
        url,
        method: "POST",
        cacheKey,
        body,
        processStreamCallback: (data: any, resultData: any[]) => {
          if (data.type === "end") return;

          // 更新结果数据
          resultData[data.page] = {
            content: data.message.content,
          };

          // 更新本地状态
          runInAction(() => {
            this.appStore.loading.progress =
              (Object.keys(resultData).length / this.pdfTotalPage) * 100;
          });
        },
        resultDataType: "array",
        totalMessages: this.pdfTotalPage,
      });
      runInAction(() => {
        this.recognitionResults = recognitionResults;
      });
      this.appStore.hideLoading();
    } catch (error) {
      console.error("Error in processImageRecognition:", error);
      this.appStore.showLoading(
        "图像识别失败",
        0,
        (error as Error).message || "未知错误"
      );
      message.error("图像识别失败");
      return;
    }
  }

  async handleImageUpload() {
    if (!this.file) {
      message.warning("请先选择文件");
      return;
    }
    this.pdfMd5 = "（图片无MD5）";

    const reader = new FileReader();
    reader.onload = async (e) => {
      runInAction(() => {
        this.pdfTotalPage = 1; // 图片文件只有一页
        this.base64Images = [e.target?.result as string];
      });
    };
    reader.readAsDataURL(this.file);
  }
  async handlePdfUpload() {
    if (!this.file) {
      message.warning("请先选择文件");
      return;
    }
    const { appStore } = this;
    appStore.showLoading("上传文件中");

    const response = await uploadFile(this.file, appStore.basePath, {
      currentLanguage: appStore.settings.currentLanguage,
      tenantId: appStore.tenantId,
      tenantEnv: appStore.tenantEnv,
      studyNum: appStore.studyNum,
      userId: appStore.userId,
      userName: appStore.userName,
    });

    appStore.hideLoading();
    const rev = response?.data || {};
    if (response?.status_code != 200) {
      appStore.showLoading("上传文件失败", 0, response.status_message);
      return;
    }
    const { md5, base64_images } = rev;

    runInAction(() => {
      this.pdfMd5 = md5;
      this.pdfTotalPage = base64_images.length;
      this.base64Images = base64_images;
    });

    showNotification("图片识别完成，请完成数据标注");
    message.success("图片识别完成，请完成数据标注");
  }
}

export default FileStore;
