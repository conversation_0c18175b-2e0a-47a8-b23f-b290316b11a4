import { NextRequest, NextResponse } from 'next/server';

// 查询参数选项数据
const queryParamOptions = {
  status: ['活跃', '待激活', '禁用', '已删除'],
  role: ['admin', 'user', 'guest', 'moderator'],
  department: ['技术部', '市场部', '销售部', '人事部', '财务部'],
  category: ['电子产品', '服装', '食品', '图书', '家居', '运动', '美妆'],
  region: ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉']
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    if (!type) {
      return NextResponse.json({
        code: 40000,
        message: '缺少 type 参数',
        data: null
      });
    }

    const options = queryParamOptions[type as keyof typeof queryParamOptions];
    
    if (!options) {
      return NextResponse.json({
        code: 40400,
        message: `未找到类型 "${type}" 的选项数据`,
        data: null
      });
    }

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    return NextResponse.json({
      code: 10000,
      message: '获取成功',
      data: options
    });
  } catch (error) {
    console.error('获取查询参数选项失败:', error);
    return NextResponse.json({
      code: 50000,
      message: '服务器内部错误',
      data: null
    });
  }
}
