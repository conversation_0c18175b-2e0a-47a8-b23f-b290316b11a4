'use client'
import React, { useState, useEffect } from 'react';
import { useStateStore } from '../../StateContext';
import { observer } from 'mobx-react-lite'; 
import MarkdownView from './MarkdownView';
import Image from 'next/image';
import type { Editor as EditorType  } from 'ckeditor5';
 

const ImageResultsContainer: React.FC = () => {
  const appStore = useStateStore();
  const images = appStore.fileStore.base64Images;
  const fileName = appStore.fileStore.fileName;
 
  const [activeEditor, setActiveEditor] = useState<EditorType | null>(null);
 
  const handleFocus = (editor: EditorType) => {
    setActiveEditor(editor);
  }
  const handleChange = (editor: EditorType, content: string) => {

  }
 
  if(appStore.fileStore.base64Images.length === 0 ) {
    return null;
  }
   
  return (
    <div className="image-results-wrapper">
      {images.map((imageData, index) => (
        <div key={index} className="image-result-container">
          <div className="image-panel"> 
           <Image src={imageData} alt={`Page ${index + 1}`} width={500} height={500} /> 
          </div>
          
          <div className="result-panel">
          <div className="recognition-content">
            <MarkdownView onChange={handleChange} onFocus={handleFocus} initialData={appStore.fileStore.recognitionResults[index].content} /> 
          </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default observer(ImageResultsContainer);