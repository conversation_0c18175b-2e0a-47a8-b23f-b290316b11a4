# API表格动态查询参数功能实现报告

**日期**: 2025年1月9日  
**任务**: 为API表格组件添加动态查询参数功能

## 任务目标

为 EnhancedApiTable 组件添加动态查询参数功能，具体要求：
- 可以指定查询参数的动态值
- 加一个查询参数的配置，使用api-caller的定义，新增一个类型为 dict
- 需要从接口中加载回来，返回值为data: string[]，默认取第一个作为查询参数
- 在搜索框右边加入查询参数选择界面

## 执行步骤

### 1. 扩展 api-caller 支持 dict 类型

**修改文件**: `src/app/MarkdownPlus/api-caller-extension.tsx`

- 在 `ApiParam` 接口中添加了 `dict` 类型支持
- 添加了 `dictUrl` 和 `dictOptions` 属性
- 实现了 `loadDictData` 函数来异步加载 dict 数据
- 在 `ApiParamInput` 组件中添加了 dict 类型的下拉选择器
- 支持加载状态显示和错误处理

```typescript
interface ApiParam {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'json' | 'enum' | 'file' | 'files' | 'dict';
  required: boolean;
  defaultValue?: any;
  description?: string;
  enumOptions?: string[];
  accept?: string;
  dictUrl?: string; // dict 类型的数据源 URL
  dictOptions?: string[]; // dict 类型加载的选项数据
}
```

### 2. 为 enhanced-api-table 添加查询参数配置

**修改文件**: `src/app/MarkdownPlus/enhanced-api-table.tsx`

- 定义了 `QueryParamConfig` 接口
- 在 `EnhancedApiTableProps` 中添加了 `queryParams` 配置
- 添加了查询参数状态管理：`queryParamValues`, `queryParamOptions`, `queryParamLoading`
- 实现了 `loadQueryParamOptions` 函数来加载查询参数选项
- 在 `buildApiUrl` 中添加了查询参数的处理

```typescript
interface QueryParamConfig {
  name: string; // 参数名称
  label: string; // 显示标签
  type: 'dict'; // 目前只支持 dict 类型
  dictUrl: string; // dict 数据源 URL
  defaultValue?: string; // 默认值
}
```

### 3. 实现查询参数选择界面

**修改文件**: `src/app/MarkdownPlus/enhanced-api-table.tsx`

- 在搜索框右边添加了查询参数选择器
- 支持多个查询参数同时显示
- 实现了参数值变化时的实时过滤
- 添加了加载状态显示
- 参数变化时自动重置到第一页

界面布局：
```
[搜索框] [参数1: 下拉框] [参数2: 下拉框] ... [刷新按钮]
```

### 4. 更新 markdown 扩展支持新配置

**修改文件**: `src/app/MarkdownPlus/marked-extensions.tsx`

- 在组件创建时添加了 `queryParams` 参数传递
- 更新了增强功能检测逻辑

### 5. 创建测试API和测试页面

**新增文件**:
- `src/app/api/query-params/route.ts` - 查询参数选项API
- `src/app/query-params-test/page.tsx` - 测试页面

**修改文件**:
- `src/app/api/test-data/route.ts` - 支持查询参数过滤

## 遇到的问题

### 1. React Hook 依赖问题
**问题**: useCallback 和 useEffect 的依赖数组不完整导致警告。

**解决方案**: 正确添加所有依赖项，使用 useCallback 包装异步函数。

### 2. 数据过滤逻辑
**问题**: 需要同时支持搜索过滤和查询参数过滤。

**解决方案**: 修改 `filterData` 函数，支持多种过滤条件的组合（AND逻辑）。

### 3. 默认值处理
**问题**: 查询参数的默认值需要在数据加载完成后设置。

**解决方案**: 在 `loadQueryParamOptions` 函数中检查是否需要设置默认值。

## 解决方案

### 核心功能实现

1. **动态选项加载**: 从API接口异步加载查询参数选项
2. **实时过滤**: 选择查询参数后立即触发数据重新加载
3. **多参数支持**: 支持同时使用多个查询参数进行组合过滤
4. **默认值支持**: 支持为查询参数设置默认值
5. **用户体验优化**: 加载状态显示、错误处理、响应式布局

### 技术特点

- **类型安全**: 使用 TypeScript 确保类型安全
- **异步处理**: 支持异步加载选项数据
- **状态管理**: 完善的状态管理和生命周期处理
- **错误处理**: 完善的错误处理和用户反馈
- **性能优化**: 使用 useCallback 和 useMemo 优化性能

## 最终结果

✅ **功能完全实现**:
- dict 类型参数支持正常工作
- 查询参数选择器正确显示在搜索框右侧
- 选项数据从API正确加载
- 默认值功能正常工作
- 实时过滤功能正常工作
- 多参数组合过滤正常工作

✅ **测试验证**:
- 创建了完整的测试页面 `/query-params-test`
- 包含多种测试用例（单参数、多参数、默认值）
- 所有功能都经过浏览器测试验证

✅ **代码质量**:
- 模块化设计，易于扩展
- TypeScript 类型安全
- 完善的错误处理
- 良好的用户体验

## 使用示例

```markdown
\`\`\`api-table:/api/test-data?type=users
{
  "pageSize": 10,
  "showSearch": true,
  "showPagination": true,
  "searchPlaceholder": "搜索用户...",
  "queryParams": [
    {
      "name": "status",
      "label": "状态",
      "type": "dict",
      "dictUrl": "/api/query-params?type=status"
    },
    {
      "name": "department",
      "label": "部门",
      "type": "dict",
      "dictUrl": "/api/query-params?type=department",
      "defaultValue": "技术部"
    }
  ]
}
\`\`\`
```

## 后续建议

1. **扩展参数类型**: 可以考虑支持更多查询参数类型（如日期范围、数字范围等）
2. **缓存优化**: 可以添加选项数据的缓存机制，避免重复请求
3. **批量操作**: 可以考虑添加批量选择和清空功能
4. **URL同步**: 可以考虑将查询参数同步到URL中，支持书签和分享

## 相关文件

- `src/app/MarkdownPlus/api-caller-extension.tsx` - dict类型支持
- `src/app/MarkdownPlus/enhanced-api-table.tsx` - 主要功能实现
- `src/app/MarkdownPlus/marked-extensions.tsx` - markdown扩展更新
- `src/app/api/query-params/route.ts` - 查询参数选项API
- `src/app/api/test-data/route.ts` - 测试数据API更新
- `src/app/query-params-test/page.tsx` - 测试页面
