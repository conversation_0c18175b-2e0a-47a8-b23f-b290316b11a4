/**
 * 事件数据值类型，可以是对象、字符串或null
 */
type EventDataValue = { type: string } & Record<string, unknown> ;

/**
 * 上传结果类型
 */
type UploadResult = EventDataValue | null ; 

/**
 * 事件对象接口，表示从服务器接收的事件
 */
interface EventData {
  type: string;
  data: EventDataValue | null; 
}

/**
 * 上传选项接口
 */
interface UploadOptions {
  onEvent?: (event: EventData) => void;
  onComplete?: (result: UploadResult) => void; 
  onError?: (error: Error) => void;
  extraData?: Record<string, string | number | boolean | Blob>;
}

/**
 * 上传文件并接收服务器返回的事件流
 * @param url - 上传地址
 * @param files - 单个文件或文件数组
 * @param options - 配置选项
 * @returns 完成后的结果
 */
async function uploadFileWithEventStream(
  url: string, 
  files: File | File[], 
  options: UploadOptions = {}
): Promise<UploadResult> {
  // 默认配置
  const config: Required<UploadOptions> = {
    onEvent: (event: EventData) => console.log('收到事件:', event), // 事件处理回调
    onComplete: (result: UploadResult) => console.log('上传完成:', result), // 完成回调 
    onError: (error: Error) => console.error('错误:', error), // 错误回调
    extraData: {}, // 附加数据
    ...options
  } as Required<UploadOptions>; // 类型断言，因为options可能不完整

  // 准备表单数据
  const formData = new FormData();
  
  // 添加文件到表单
  if (Array.isArray(files)) {
    files.forEach((file: File, index: number) => {
      formData.append(`file[${index}]`, file);
    });
  } else {
    formData.append('file', files);
  }
  
  // 添加额外数据
  Object.entries(config.extraData).forEach(([key, value]: [string, string | number | boolean | Blob]) => {
    formData.append(key, String(value));
  });

  try {
    // 发送请求
    const response = await fetch(url, {
      method: 'POST',
      body: formData,
      headers: {
        'Accept': 'text/event-stream', // 表明客户端接受事件流响应
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    if (!response.body) {
      throw new Error('Response body is null');
    }
    
    // 获取可读流
    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8')
    let buffer = '';
    let finalResult: UploadResult = null;
    
    // 处理事件流
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        // 处理最后可能剩余的数据
        if (buffer.trim()) {
          processEventData(buffer, config);
        }
        break;
      }
      
      // 解码数据块
      const chunk = decoder.decode(value, { stream: true });
      buffer += chunk;
      
      // 查找完整事件（以双换行符分隔）
      let boundaryIndex: number;
      while ((boundaryIndex = buffer.indexOf('\n\n')) !== -1) {
        const eventText = buffer.substring(0, boundaryIndex);
        buffer = buffer.substring(boundaryIndex + 2);
        
        const event = processEventData(eventText, config);
        
        // 检查是否是最终结果事件
        if (event.type === 'complete' || event.type === 'end') {
          finalResult = event.data;
        }
      }
    }
    
    // 上传和事件流都完成了
    if (config.onComplete) {
      config.onComplete(finalResult);
    }
    
    return finalResult;
    
  } catch (error) {
    if (config.onError) {
      config.onError(error as Error);
    }
    throw error;
  }
}

/**
 * 处理单个事件数据
 * @param eventText - 事件文本
 * @param config - 配置对象
 * @returns 处理后的事件对象
 */
function processEventData(eventText: string, config: Required<UploadOptions>): EventData {
  const lines = eventText.split('\n');
  const event: EventData = {
    type: 'message',  // 默认事件类型
    data: null
  };
  
  // 解析事件内容
  for (const line of lines) {
    if (line.startsWith('event:')) {
      event.type = line.substring(6).trim();
    } 
    else if (line.startsWith('data:')) {
      const dataContent = line.substring(5).trim();
      
      // 尝试解析JSON数据
      try {
        // 尝试解析为对象，如果失败则保留为字符串
        const parsedData = JSON.parse(dataContent);
        event.data = typeof parsedData === 'object' ? parsedData : dataContent;
      } catch (e) {
        event.data = { type: 'text', content: dataContent };
      }
    } 
  }
  
  // 调用事件处理回调
  if (config.onEvent) {
    config.onEvent(event);
  }
  
  return event;
}

export { 
  uploadFileWithEventStream, 
  type UploadOptions, 
  type EventData, 
  type EventDataValue, 
  type UploadResult 
};



/**
 * 根据JSON数据生成纯文本格式的药物不良反应摘要报告
 * @param {Object} reportData - 报告JSON数据
 * @returns {string} 纯文本格式的摘要报告
 */
export function generatePlainTextAdverseEventSummary(reportData:any) {
  // 提取关键数据
  const baseInfo = reportData.reportBaseInfo;
  const subjectInfo = reportData.reportSubjectInfo;
  const drugInfo = reportData.drugInfo["试验用药"] || [];
  const adverseEvents = reportData.reportSaeDetailInfo || [];
  const evaluations = reportData["药品和不良反应评价矩阵"] || [];
  const narrativeInfo = reportData.saeDescribe || {};
  
  // 格式化日期函数
  const formatDate = (dateStr:string) => {
    if (!dateStr) return '未知';
    return dateStr;
  };
  
  // 创建表格分隔线
  const createSeparator = (length = 80) => {
    return '-'.repeat(length);
  };
  
  // 创建表格行
  const createRow = (columns: string[], widths: number[]) => {
    let row = '';
    columns.forEach((col, index) => {
      const colText = col.toString().padEnd(widths[index]);
      row += colText;
    });
    return row;
  };
  
  // 构建纯文本摘要
  let summary = '';
  
  // 标题
  summary += '药物不良反应报告摘要\n';
  summary += '====================\n\n';
  
  // 1. 基本信息
  summary += '1. 基本信息\n';
  summary += createSeparator() + '\n';
  summary += `报告类型:     ${baseInfo.TXT_report_type || '未知'}\n`;
  summary += `报告国家:     ${baseInfo.country_text || '未知'}\n`;
  summary += `初始报告日期: ${formatDate(baseInfo.init_rept_date)}\n`;
  summary += `报告编号:     ${baseInfo.CSM_UD_NUMBER_2 || '未知'}\n`;
  summary += '\n';
  
  // 2. 患者信息
  summary += '2. 患者信息\n';
  summary += createSeparator() + '\n';
  summary += `性别: ${subjectInfo.TXT_pat_gender || '未知'}   `;
  summary += `年龄: ${subjectInfo.pat_age ? `${subjectInfo.pat_age} ${subjectInfo.TXT_pat_age_unit || ''}` : '未知'}   `;
  summary += `体重: ${subjectInfo.pat_weight ? `${subjectInfo.pat_weight} kg` : '未知'}\n`;
  summary += `民族: ${subjectInfo.TXT_pat_ethnic_group_id || '未知'}\n\n`;
  
  // 相关病史
  summary += '相关病史:\n';
  if (subjectInfo.rel_hist_add && subjectInfo.rel_hist_add.length > 0) {
    subjectInfo.rel_hist_add.forEach((hist:any, index:number) => {
      summary += `${index + 1}. ${hist.TXT_Rel_Hist_Table_0_pat_hist_type || ''}: ${hist.Rel_Hist_Table_0_pat_hist_rptd || ''}\n`;
    });
  } else {
    summary += '无相关病史记录\n';
  }
  summary += '\n';
  
  // 3. 药品信息
  summary += '3. 药品信息\n';
  summary += createSeparator() + '\n';
  
  // 药品表头
  const drugWidths = [20, 20, 15, 12, 12, 20];
  summary += createRow(['药品名称', '用量', '给药途径', '开始日期', '停止日期', '适应症'], drugWidths) + '\n';
  summary += createSeparator() + '\n';
  
  // 药品数据
  drugInfo.forEach((drug:any) => {
    const doseInfo = drug.expDoseTable && drug.expDoseTable.length > 0 ? drug.expDoseTable[0] : {};
    const indications = drug.btnAddInd && drug.btnAddInd.length > 0 ? 
        drug.btnAddInd.map((ind:any) => ind.Ind_Table_0_ind_reptd).join(', ') : '未知';
    
    const dose = `${doseInfo.cdr_dose || ''} ${doseInfo.TXT_cdr_dose_unit_id || ''}, ${doseInfo.TXT_freq_id || ''}`;
    
    const cols = [
      drug.product_name || '未知',
      dose,
      doseInfo.TXT_cdr_admin_route_id || '未知',
      formatDate(doseInfo.start_datetime),
      formatDate(doseInfo.stop_datetime),
      indications
    ];
    
    summary += createRow(cols, drugWidths) + '\n';
  });
  summary += '\n';
  
  // 4. 不良反应信息
  summary += '4. 不良反应信息\n';
  summary += createSeparator() + '\n';
  
  // 不良反应表头
  const aeWidths = [25, 15, 20, 20];
  summary += createRow(['不良反应', '发生日期', '严重性', '结局'], aeWidths) + '\n';
  summary += createSeparator() + '\n';
  
  // 不良反应数据
  adverseEvents.forEach((event:any) => {
    const seriousness = event.sc_non_serious === '1' ? '非严重' : 
      (event.sc_death === '1' ? '死亡' : 
        (event.sc_threat === '1' ? '危及生命' : 
          (event.sc_hosp === '1' ? '住院/延长住院' : 
            (event.sc_disable === '1' ? '导致残疾' : 
              (event.sc_cong_anom === '1' ? '先天性异常' : '未知')))));
    
    const cols = [
      event.desc_reptd || '未知',
      formatDate(event.onset),
      seriousness,
      event.TXT_evt_outcome_id || '未知'
    ];
    
    summary += createRow(cols, aeWidths) + '\n';
  });
  summary += '\n';
  
  // 5. 药品不良反应因果关系评价
  summary += '5. 药品不良反应因果关系评价\n';
  summary += createSeparator() + '\n';
  
  // 评价表头
  const evalWidths = [15, 15, 15, 15, 15, 15];
  summary += createRow(['药品名称', '不良反应', '停药措施', '停药后反应', '评价来源', '因果关系'], evalWidths) + '\n';
  summary += createSeparator() + '\n';
  
  // 评价数据
  evaluations.forEach((_eval:any) => {
    const dechallenge = _eval.dechallenge === '1' ? '好转' : 
                       (_eval.dechallenge === '2' ? '未好转' : 
                       (_eval.dechallenge === '3' ? '未知' : '未知'));
    
    const cols = [
      _eval.product_name || '未知',
      _eval.desc_reptd || '未知',
      _eval.TXT_act_taken_id || '未知',
      dechallenge,
      _eval.评价列表 && _eval.评价列表.length > 0 ? _eval.评价列表[0].source_id : '未知',
      _eval.评价列表 && _eval.评价列表.length > 0 ? _eval.评价列表[0].causality_id : '未知'
    ];
    
    summary += createRow(cols, evalWidths) + '\n';
  });
  summary += '\n';
  
  // 6. 病例描述
  summary += '6. 病例描述\n';
  summary += createSeparator() + '\n';
  summary += narrativeInfo.narrative || '无相关描述';
  summary += '\n\n';
  
  // 生成时间
  summary += createSeparator() + '\n';
  summary += `摘要生成时间: ${new Date().toISOString().substring(0, 10)}\n`;
  
  return summary;
}
 
/**
 * 根据JSON数据生成药物不良反应摘要报告
 * @param {Object} reportData - 报告JSON数据
 * @returns {string} HTML格式的摘要报告
 */
export function generateAdverseEventSummary(reportData:any) {
  // 提取关键数据
  const baseInfo = reportData.reportBaseInfo;
  const subjectInfo = reportData.reportSubjectInfo;
  const drugInfo = reportData.drugInfo["试验用药"] || [];
  const adverseEvents = reportData.reportSaeDetailInfo || [];
  const evaluations = reportData["药品和不良反应评价矩阵"] || [];
  const narrativeInfo = reportData.saeDescribe || {}; 
  // 格式化日期函数
  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    return dateStr;
  };
  
  // 构建HTML摘要
  let summary = `
    <div class="report-summary">
      <h2>药物不良反应报告摘要</h2>
      
      <div class="section">
        <h3>1. 基本信息</h3>
        <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
          <tr>
            <th style="width: 30%; background-color: #f2f2f2;">报告类型</th>
            <td>${baseInfo.TXT_report_type || ''}</td>
          </tr>
          <tr>
            <th style="background-color: #f2f2f2;">报告国家</th>
            <td>${baseInfo.country_text || ''}</td>
          </tr>
          <tr>
            <th style="background-color: #f2f2f2;">初始报告日期</th>
            <td>${formatDate(baseInfo.init_rept_date)}</td>
          </tr>
          <tr>
            <th style="background-color: #f2f2f2;">报告编号</th>
            <td>${baseInfo.CSM_UD_NUMBER_2 || ''}</td>
          </tr>
        </table>
      </div>

      <div class="section">
        <h3>2. 患者信息</h3>
        <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
          <tr>
            <th style="width: 30%; background-color: #f2f2f2;">性别</th>
            <td>${subjectInfo.TXT_pat_gender || ''}</td>
          </tr>
          <tr>
            <th style="background-color: #f2f2f2;">年龄</th>
            <td>${subjectInfo.pat_age ? `${subjectInfo.pat_age} ${subjectInfo.TXT_pat_age_unit || ''}` : ''}</td>
          </tr>
          <tr>
            <th style="background-color: #f2f2f2;">体重</th>
            <td>${subjectInfo.pat_weight ? `${subjectInfo.pat_weight} kg` : ''}</td>
          </tr>
          <tr>
            <th style="background-color: #f2f2f2;">民族</th>
            <td>${subjectInfo.TXT_pat_ethnic_group_id || ''}</td>
          </tr>
        </table>
        
        <h4>相关病史:</h4>
        <ul>
  `;
  
  // 添加相关病史
  if (subjectInfo.rel_hist_add && subjectInfo.rel_hist_add.length > 0) {
    subjectInfo.rel_hist_add.forEach((hist:any) => {
      summary += `<li><strong>${hist.TXT_Rel_Hist_Table_0_pat_hist_type || ''}:</strong> ${hist.Rel_Hist_Table_0_pat_hist_rptd || ''}</li>`;
    });
  } else {
    summary += `<li>无相关病史记录</li>`;
  }
  
  summary += `
        </ul>
      </div>
      
      <div class="section">
        <h3>3. 药品信息</h3>
        <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
          <tr style="background-color: #f2f2f2;">
            <th>药品名称</th>
            <th>用量</th>
            <th>给药途径</th>
            <th>开始日期</th>
            <th>停止日期</th>
            <th>适应症</th>
          </tr>
  `;
  
  // 添加药品信息
  drugInfo.forEach((drug:any) => {
    const doseInfo = drug.expDoseTable && drug.expDoseTable.length > 0 ? drug.expDoseTable[0] : {};
    const indications = drug.btnAddInd && drug.btnAddInd.length > 0 ? 
        drug.btnAddInd.map((ind:any) => ind.Ind_Table_0_ind_reptd).join(', ') : '';
    
    summary += `
      <tr>
        <td>${drug.product_name || ''}</td>
        <td>${doseInfo.cdr_dose || ''} ${doseInfo.TXT_cdr_dose_unit_id || ''}, ${doseInfo.TXT_freq_id || ''}</td>
        <td>${doseInfo.TXT_cdr_admin_route_id || ''}</td>
        <td>${formatDate(doseInfo.start_datetime)}</td>
        <td>${formatDate(doseInfo.stop_datetime)}</td>
        <td>${indications}</td>
      </tr>
    `;
  });
  
  summary += `
        </table>
      </div>
      
      <div class="section">
        <h3>4. 不良反应信息</h3>
        <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
          <tr style="background-color: #f2f2f2;">
            <th>不良反应</th>
            <th>发生日期</th>
            <th>严重性</th>
            <th>结局</th>
          </tr>
  `;
  
  // 添加不良反应信息
  adverseEvents.forEach((event:any) => {
    const seriousness = event.sc_non_serious === '1' ? '非严重' : 
      (event.sc_death === '1' ? '死亡' : 
        (event.sc_threat === '1' ? '危及生命' : 
          (event.sc_hosp === '1' ? '住院或延长住院' : 
            (event.sc_disable === '1' ? '导致残疾' : 
              (event.sc_cong_anom === '1' ? '先天性异常' : '未知')))));
    
    summary += `
      <tr>
        <td>${event.desc_reptd || ''}</td>
        <td>${formatDate(event.onset)}</td>
        <td>${seriousness}</td>
        <td>${event.TXT_evt_outcome_id || ''}</td>
      </tr>
    `;
  });
  
  summary += `
        </table>
      </div>
      
      <div class="section">
        <h3>5. 药品不良反应因果关系评价</h3>
        <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
          <tr style="background-color: #f2f2f2;">
            <th>药品名称</th>
            <th>不良反应</th>
            <th>停药措施</th>
            <th>停药后反应</th>
            <th>评价来源</th>
            <th>因果关系</th>
          </tr>
  `;
  
  // 添加因果关系评价
  evaluations.forEach((_eval:any) => {
    let evaluationDetails = '';
    if (_eval.评价列表 && _eval.评价列表.length > 0) {
      _eval.评价列表.forEach((item:any) => {
        evaluationDetails += `${item.source_id || ''}: ${item.causality_id || ''}<br>`;
      });
    }
    
    const dechallenge = _eval.dechallenge === '1' ? '好转' : 
                       (_eval.dechallenge === '2' ? '未好转' : 
                       (_eval.dechallenge === '3' ? '未知' : ''));
    
    summary += `
      <tr>
        <td>${_eval.product_name || ''}</td>
        <td>${_eval.desc_reptd || ''}</td>
        <td>${_eval.TXT_act_taken_id || ''}</td>
        <td>${dechallenge}</td>
        <td>${_eval.评价列表 && _eval.评价列表.length > 0 ? _eval.评价列表[0].source_id : ''}</td>
        <td>${_eval.评价列表 && _eval.评价列表.length > 0 ? _eval.评价列表[0].causality_id : ''}</td>
      </tr>
    `;
  });
  
  summary += `
        </table>
      </div>
      
      <div class="section">
        <h3>6. 病例描述</h3>
        <p style="border: 1px solid #ddd; padding: 10px; background-color: #f9f9f9;">
          ${narrativeInfo.narrative || '无相关描述'}
        </p>
      </div>
    </div>
  `;
  
  return summary;
} 