version: '3.8'

services: 

  # test环境配置
  test:
    build:
      context: .
      network: host
    container_name: pv-manus-front-test
    user: root
    ports:
      - "3201:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    restart: unless-stopped
    volumes:
      - /home/<USER>/test/volumes/documents:/app/documents
      - /home/<USER>/test/volumes/reports:/app/reports
      - /usr/share/nginx/html/test/argus_robot:/app/public