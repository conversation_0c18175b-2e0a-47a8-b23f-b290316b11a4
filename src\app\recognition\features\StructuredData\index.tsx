'use client'
import React, { useState, useEffect } from 'react';
import { useStateStore } from '../../StateContext';
import { observer } from 'mobx-react-lite'; 
interface StructuredDataProps {
  isOpen?: boolean;
  onClose: () => void;
  data?: any;
  onDataChange?: (data: any) => void;
}

const StructuredData: React.FC<StructuredDataProps> = ({
  isOpen = true,
  onClose,
  data,
  onDataChange
}) => {
  const appStore = useStateStore();
  const [structuredData, setStructuredData] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'json' | 'table'>('json');

  useEffect(() => {
    if (data) {
      setStructuredData(data);
    } else if (isOpen && appStore.finalStructuredData) {
      setStructuredData(appStore.finalStructuredData);
    }
  }, [isOpen, appStore.finalStructuredData, data]);

  if (!isOpen) return null;

  const renderAsTable = (data: any) => {
    if (!data || typeof data !== 'object') return <p>No structured data available</p>;

    // For array of objects, create a table
    if (Array.isArray(data) && data.length > 0 && typeof data[0] === 'object') {
      const headers = Object.keys(data[0]);
      
      return (
        <div className="table-container">
          <table className="data-table">
            <thead>
              <tr>
                {headers.map((header, index) => (
                  <th key={index}>{header}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {data.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {headers.map((header, colIndex) => (
                    <td key={colIndex}>{typeof row[header] === 'object' ? JSON.stringify(row[header]) : row[header]}</td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    }

    // For a single object, display as key-value pairs
    if (!Array.isArray(data) && typeof data === 'object') {
      return (
        <div className="table-container">
          <table className="data-table">
            <thead>
              <tr>
                <th>Key</th>
                <th>Value</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(data).map(([key, value], index) => (
                <tr key={index}>
                  <td>{key}</td>
                  <td>{typeof value === 'object' ? JSON.stringify(value) : String(value)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    }

    return <p>Unsupported data format</p>;
  };

  const renderAsJson = (data: any) => {
    return (
      <pre className="json-view">
        {JSON.stringify(data, null, 2)}
      </pre>
    );
  };

  // 处理导出数据
  const handleExportData = () => {
    if (!structuredData) return;
    
    // 创建下载链接
    const dataStr = JSON.stringify(structuredData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = 'structured_data.json';
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    document.body.appendChild(linkElement);
    linkElement.click();
    document.body.removeChild(linkElement);
  };
  
  return (
    <div className="modal-overlay">
      <div className="modal-content structured-data-modal">
        <div className="modal-header">
          <h5 className="modal-title">结构化数据</h5>
          <div className="view-toggle">
            <button 
              className={`btn ${viewMode === 'json' ? 'btn-primary' : 'btn-outline-primary'}`}
              onClick={() => setViewMode('json')}
            >
              JSON
            </button>
            <button 
              className={`btn ${viewMode === 'table' ? 'btn-primary' : 'btn-outline-primary'}`}
              onClick={() => setViewMode('table')}
            >
              表格
            </button>
          </div>
          <button 
            type="button" 
            className="close-button"
            onClick={onClose}
          >
            ×
          </button>
        </div>
        
        <div className="modal-body">
          {structuredData ? (
            viewMode === 'json' ? renderAsJson(structuredData) : renderAsTable(structuredData)
          ) : (
            <p className="no-data">暂无结构化数据</p>
          )}
        </div>
        
        <div className="modal-footer">
          <button 
            type="button" 
            className="btn btn-secondary"
            onClick={onClose}
          >
            关闭
          </button>
          <button 
            type="button" 
            className="btn btn-primary"
            onClick={handleExportData}
            disabled={!structuredData}
          >
            导出数据
          </button>
        </div>
      </div>
    </div>
  );
};

 

export default observer(StructuredData);