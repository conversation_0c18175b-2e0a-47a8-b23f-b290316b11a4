'use client'
import React from 'react';
import { useStateStore } from '../StateContext';
import InfoCard from '../ui/InfoCard';
import { observer } from 'mobx-react-lite';

const InfoSection: React.FC = () => {
  const appStore = useStateStore(); 
  const { fileStore } = appStore; 
  return (
    <div className="info-section">
      <InfoCard 
        title="环境信息"
        items={[
          { label: "租户环境", id: "tenantEnv", value: appStore.settings.environment },
          { label: "语言环境", id: "languageEnv", value: appStore.settings.currentLanguage }
        ]}
      />
      
      <InfoCard 
        title="租户信息"
        items={[
          { label: "租户名称", id: "tenantName", value: appStore.tenantName || '-' },
          { label: "租户编号", id: "tenantId", value: appStore.tenantId || '-' }
        ]}
      />
      
      <InfoCard 
        title="用户信息"
        items={[
          { label: "用户名", id: "userName", value: appStore.userName || '-' },
          { label: "用户ID", id: "userId", value: appStore.userId || '-' },
          { label: "用户角色", id: "userRole", value: appStore.settings.operationMode === 'auto' ? '普通用户' : '管理员' }
        ]}
      />
      
      <InfoCard 
        title="项目信息"
        key={fileStore.fileName + fileStore.pdfMd5}
        items={[
          { label: "方案编号", id: "studyNum", value: appStore.studyNum || '-' },
          { label: "文件名称", id: "fileName", value: fileStore.fileName || '未上传' },
          { label: "文件MD5", id: "fileMD5", value: fileStore.pdfMd5 || '未上传' }
        ]}
      /> 

    </div>
  );
};

export default observer(InfoSection);