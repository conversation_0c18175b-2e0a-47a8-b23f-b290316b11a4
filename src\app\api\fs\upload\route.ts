import { NextRequest, NextResponse } from "next/server";
import { upload, DIR } from "@/utils/fs";

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    // 获取表单数据
    const type = formData.get("type") as DIR;
    const path = formData.get("path") as string;
    const content = formData.get("content") as string; 

    if (!path) {
      return NextResponse.json({ error: "文件路径不能为空" }, { status: 400 });
    }
    if (!content) {
      return NextResponse.json({ error: "文件内容不能为空" }, { status: 400 });
    }

    const res = await upload(type, path, content);

    return NextResponse.json(res);
  } catch (error) {
    console.error("文件上传失败:", error);
    return NextResponse.json(
      { error: (error as Error).message || '文件上传失败' },
      { status: 400 }
    );
  }
}
