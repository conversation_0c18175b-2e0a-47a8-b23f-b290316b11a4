import { exec } from "child_process";
import { promisify } from "util";
import cron from "node-cron";
import fs from "fs/promises";
import path from "path";

const execAsync = promisify(exec);

/**
 * Git操作类
 */
export class GitUtils {
  private repoPath: string;
  private branchName: string;

  constructor(repoPath: string, branchName: string) {
    this.repoPath = repoPath;
    this.branchName = branchName;
  }
  async init() {
    try {
      await fs.stat(path.join(this.repoPath, "./.git"));
      await this.pull();
      this.startAutoCommit();
    } catch (e) {
      console.error("未找到正确的git仓库");
    }
  }
  async pull() {
    try {
      // 检查是否有未提交的更改
      const { stdout } = await execAsync('git status --porcelain', { cwd: this.repoPath });
      let hasStash = false;

      if (stdout.trim()) {
        // 有未提交的更改，先stash
        console.log('检测到未提交的更改，临时保存中...');
        await execAsync('git stash', { cwd: this.repoPath });
        hasStash = true;
      }

      try {
        // 拉取最新代码
        await execAsync(`git pull origin ${this.branchName}`, {
          cwd: this.repoPath,
        });
        console.log("拉取最新文件成功");

        // 如果之前有stash，现在恢复它
        if (hasStash) {
          console.log('正在恢复未提交的更改...');
          await execAsync('git stash pop', { cwd: this.repoPath });
          console.log('未提交的更改已恢复');
        }
      } catch (pullError) {
        // 如果pull失败且有stash，尝试恢复stash
        if (hasStash) {
          console.log('拉取失败，正在恢复未提交的更改...');
          await execAsync('git stash pop', { cwd: this.repoPath });
        }
        throw pullError;
      }
    } catch (error) {
      console.error("操作失败:", error);
      throw error;
    }
  }

  async commitAll(commitMessage: string = "auto commit"): Promise<void> {
    try {
      // 检查是否有未提交的更改
      const { stdout } = await execAsync('git status --porcelain', { cwd: this.repoPath });
      if (!stdout.trim()) {
        console.log('没有需要提交的更改');
        return;
      }

      // 添加所有更改的文件
      await execAsync("git add .", { cwd: this.repoPath });

      // 提交更改
      await execAsync(`git commit -m "${commitMessage}"`, {
        cwd: this.repoPath,
      });

      // 推送到远程仓库
      await execAsync(`git push origin ${this.branchName}`, { cwd: this.repoPath });

      console.log("文件提交并推送成功");
    } catch (error) {
      console.error("提交文件失败:", error);
      throw error;
    }
  }

  startAutoCommit(
    cronExpression: string = "*/5 * * * *",
    commitMessage: string = "auto commit"
  ): void {
    cron.schedule(cronExpression, async () => {
      try {
        await this.commitAll(commitMessage);
        console.log(`自动提交成功 - ${new Date().toLocaleString()}`);
      } catch (error) {
        console.error("自动提交失败:", error);
      }
    });

    console.log("自动提交定时任务已开启");
  }
 
  async getStatus(): Promise<string> {
    try {
      const { stdout } = await execAsync("git status", { cwd: this.repoPath });
      return stdout;
    } catch (error) {
      console.error("获取仓库状态失败:", error);
      throw error;
    }
  }
}

const REPORT_BASE_DIR = process.env.REPORTBUG_BASE_DIR || "./reports";
export const gitClient = new GitUtils(REPORT_BASE_DIR,'issue-tracking');

