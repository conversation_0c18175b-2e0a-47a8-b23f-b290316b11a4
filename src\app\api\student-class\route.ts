import { NextRequest, NextResponse } from 'next/server';

// 标准化响应格式
function createStandardResponse(data: any, code: number = 10000, message: string = '') {
  return { data, code, message };
}

// 模拟班级数据
const classData = [
  { id: 1, name: '计算机科学与技术1班', capacity: 30, currentCount: 25 },
  { id: 2, name: '软件工程1班', capacity: 28, currentCount: 20 },
  { id: 3, name: '数据科学与大数据技术1班', capacity: 32, currentCount: 30 },
  { id: 4, name: '人工智能1班', capacity: 25, currentCount: 18 },
  { id: 5, name: '网络工程1班', capacity: 30, currentCount: 22 }
];

// 模拟学生数据 (需要修改，所以使用let)
// eslint-disable-next-line prefer-const
let studentData = [
  { id: 1, name: '张三', age: 20, gender: '男', major: '计算机科学与技术', classId: null, className: null },
  { id: 2, name: '李四', age: 19, gender: '女', major: '软件工程', classId: null, className: null },
  { id: 3, name: '王五', age: 21, gender: '男', major: '数据科学与大数据技术', classId: null, className: null },
  { id: 4, name: '赵六', age: 20, gender: '女', major: '人工智能', classId: null, className: null },
  { id: 5, name: '钱七', age: 19, gender: '男', major: '网络工程', classId: null, className: null },
  { id: 6, name: '孙八', age: 20, gender: '女', major: '计算机科学与技术', classId: 1, className: '计算机科学与技术1班' },
  { id: 7, name: '周九', age: 21, gender: '男', major: '软件工程', classId: 2, className: '软件工程1班' },
  { id: 8, name: '吴十', age: 19, gender: '女', major: '数据科学与大数据技术', classId: 3, className: '数据科学与大数据技术1班' },
  { id: 9, name: '郑十一', age: 20, gender: '男', major: '人工智能', classId: 4, className: '人工智能1班' },
  { id: 10, name: '王十二', age: 21, gender: '女', major: '网络工程', classId: 5, className: '网络工程1班' },
  { id: 11, name: '刘十三', age: 20, gender: '男', major: '计算机科学与技术', classId: null, className: null },
  { id: 12, name: '陈十四', age: 19, gender: '女', major: '软件工程', classId: null, className: null }
];

// 数据类型到表头的映射
const dataHeaders = {
  classes: [
    { name: '班级ID', key: 'id' },
    { name: '班级名称', key: 'name' },
    { name: '容量', key: 'capacity' },
    { name: '当前人数', key: 'currentCount' },
    { name: '剩余名额', key: 'remaining' }
  ],
  students: [
    { name: '学生ID', key: 'id' },
    { name: '姓名', key: 'name' },
    { name: '年龄', key: 'age' },
    { name: '性别', key: 'gender' },
    { name: '专业', key: 'major' },
    { name: '班级ID', key: 'classId' },
    { name: '班级名称', key: 'className' }
  ]
};

// 搜索和过滤功能
function filterData(data: any[], searchTerm: string, fields: string[]) {
  if (!searchTerm) return data;
  return data.filter(item =>
    fields.some(field =>
      String(item[field] || '').toLowerCase().includes(searchTerm.toLowerCase())
    )
  );
}

// 排序功能
function sortData(data: any[], sortBy: string) {
  if (!sortBy) return data;
  const [field, direction] = sortBy.split(':');
  if (!field || !direction) return data;
  
  return [...data].sort((a, b) => {
    const aVal = a[field];
    const bVal = b[field];
    
    // 数字排序
    if (!isNaN(aVal) && !isNaN(bVal)) {
      return direction === 'ASC' ? aVal - bVal : bVal - aVal;
    }
    
    // 字符串排序
    const aStr = String(aVal || '').toLowerCase();
    const bStr = String(bVal || '').toLowerCase();
    
    if (direction === 'ASC') {
      return aStr.localeCompare(bStr);
    } else {
      return bStr.localeCompare(aStr);
    }
  });
}

// 分页功能
function paginateData(data: any[], page: number, pageSize: number) {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return {
    items: data.slice(startIndex, endIndex),
    total: data.length
  };
}

export async function GET(request: NextRequest) {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  const { searchParams } = new URL(request.url);
  const type = searchParams.get('type');
  
  // 增强表格参数
  const search = searchParams.get('search') || '';
  const sortBy = searchParams.get('sortBy') || '';
  const page = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('pageSize') || '10');

  try {
    let dataSource: any[] = [];
    let headers: any[] = [];

    switch (type) {
      case 'classes':
        // 班级数据，添加剩余名额字段
        dataSource = classData.map(cls => ({
          ...cls,
          remaining: cls.capacity - cls.currentCount
        }));
        headers = dataHeaders.classes;
        break;
      
      case 'unassigned-students':
      case 'students':
        // 未分配学生或所有学生
        if (type === 'unassigned-students') {
          dataSource = studentData.filter(student => student.classId === null);
        } else {
          dataSource = [...studentData];
        }
        headers = dataHeaders.students.filter(h =>
          type === 'unassigned-students'
            ? !['classId', 'className'].includes(h.key)
            : true
        );
        break;
      
      case 'assigned-students':
        // 已分配班级的学生
        dataSource = studentData.filter(student => student.classId !== null);
        headers = dataHeaders.students;
        break;
      
      default:
        return NextResponse.json(
          createStandardResponse(null, 40001, '无效的数据类型'),
          { status: 400 }
        );
    }

    // 应用搜索过滤
    let filteredData = [...dataSource];
    if (search) {
      const searchFields = headers.map(h => h.key);
      filteredData = filterData(filteredData, search, searchFields);
    }

    // 应用排序
    if (sortBy) {
      filteredData = sortData(filteredData, sortBy);
    }

    // 应用分页
    const { items: paginatedData, total } = paginateData(filteredData, page, pageSize);

    // 构建响应数据
    const responseData = {
      headers,
      rows: paginatedData,
      pagination: {
        total,
        page,
        pageSize
      },
      sortBy: sortBy || undefined
    };

    return NextResponse.json(createStandardResponse(responseData));

  } catch (error) {
    return NextResponse.json(
      createStandardResponse(null, 50000, '服务器内部错误'),
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  try {
    const { studentId, classId } = await request.json();

    // 验证参数
    if (!studentId || !classId) {
      return NextResponse.json(
        createStandardResponse(null, 40001, '学生ID和班级ID都是必需的'),
        { status: 400 }
      );
    }

    // 查找学生
    const studentIndex = studentData.findIndex(student => student.id === parseInt(studentId));
    if (studentIndex === -1) {
      return NextResponse.json(
        createStandardResponse(null, 40401, '学生不存在'),
        { status: 404 }
      );
    }

    const student = studentData[studentIndex];

    // 验证学生是否已分配班级
    if (student.classId !== null) {
      return NextResponse.json(
        createStandardResponse(null, 40002, `学生 ${student.name} 已经分配到班级：${student.className}`),
        { status: 400 }
      );
    }

    // 查找班级
    const targetClass = classData.find(cls => cls.id === parseInt(classId));
    if (!targetClass) {
      return NextResponse.json(
        createStandardResponse(null, 40401, '班级不存在'),
        { status: 404 }
      );
    }

    // 检查班级是否有空余名额
    if (targetClass.currentCount >= targetClass.capacity) {
      return NextResponse.json(
        createStandardResponse(null, 40003, `班级 ${targetClass.name} 已满，无法分配`),
        { status: 400 }
      );
    }

    // 分配学生到班级
    studentData[studentIndex] = {
      ...student,
      classId: targetClass.id,
      className: targetClass.name
    };

    // 更新班级当前人数
    targetClass.currentCount += 1;

    return NextResponse.json(
      createStandardResponse(
        {
          student: studentData[studentIndex],
          class: targetClass
        },
        10000,
        `成功将学生 ${student.name} 分配到班级 ${targetClass.name}`
      )
    );

  } catch (error) {
    return NextResponse.json(
      createStandardResponse(null, 50000, '服务器内部错误'),
      { status: 500 }
    );
  }
}