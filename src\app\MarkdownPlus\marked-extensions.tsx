import React from 'react';
import { createRoot } from 'react-dom/client';
import EnhancedApiTable from './enhanced-api-table';
import ApiCaller from './api-caller-extension';

// 存储组件实例的 Map
const componentInstances = new Map<string, React.ComponentType>();

// API 表格扩展
export const apiTableExtension = {
  name: 'api-table',
  level: 'block',
  start(src: string) {
    return src.match(/^```api-table:/)?.index;
  },
  tokenizer(src: string) {
    const rule = /^```api-table:([^\n]*)\n?([\s\S]*?)```/;
    const match = rule.exec(src);
    if (match) {
      const [raw, apiUrl, options = ''] = match;
      
      // 解析选项和按钮配置
      const parsedOptions: any = {};
      let buttons: any[] = [];
      
      if (options.trim()) {
        try {
          // 首先尝试解析为JSON格式（新格式）
          if (options.trim().startsWith('{')) {
            const config = JSON.parse(options.trim());
            Object.assign(parsedOptions, config);
            if (config.buttons && Array.isArray(config.buttons)) {
              buttons = config.buttons;
            }
          } else {
            // 兼容旧的键值对格式
            const optionLines = options.trim().split('\n');
            optionLines.forEach(line => {
              const [key, value] = line.split(':').map(s => s.trim());
              if (key && value) {
                parsedOptions[key] = value;
              }
            });
          }
        } catch (e) {
          console.warn('解析API表格选项失败:', e);
        }
      }

      const componentId = `api-table-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // 检测是否启用增强功能（如果有搜索、分页等配置，或者使用了 enhanced-api-table 语法）
      const isEnhanced = parsedOptions.pageSize ||
                        parsedOptions.showSearch !== undefined ||
                        parsedOptions.showPagination !== undefined ||
                        parsedOptions.searchPlaceholder ||
                        parsedOptions.queryParams;

      // 创建组件，根据配置自动启用增强功能
      const ApiTableComponent = () => React.createElement(EnhancedApiTable, {
        apiUrl: apiUrl.trim(),
        className: parsedOptions.className || 'my-4',
        defaultPageSize: parsedOptions.pageSize || 10,
        showSearch: parsedOptions.showSearch !== false, // 默认启用搜索
        showPagination: parsedOptions.showPagination !== false, // 默认启用分页
        searchPlaceholder: parsedOptions.searchPlaceholder || '搜索...',
        buttons: buttons,
        queryParams: parsedOptions.queryParams || []
      });

      componentInstances.set(componentId, ApiTableComponent);

      return {
        type: 'api-table',
        raw,
        apiUrl: apiUrl.trim(),
        options: parsedOptions,
        buttons: buttons,
        componentId
      };
    }
  },
  renderer(token: any) {
    return `<div id="${token.componentId}" class="api-table-placeholder"></div>`;
  }
};

// API 调用器扩展
export const apiCallerExtension = {
  name: 'api-caller',
  level: 'block',
  start(src: string) {
    return src.match(/^```api-caller:/)?.index;
  },
  tokenizer(src: string) {
    const rule = /^```api-caller:([\s\S]*?)```/;
    const match = rule.exec(src);
    if (match) {
      const [raw, definitionStr] = match;
      
      try {
        // 解析API定义
        const definition = JSON.parse(definitionStr.trim());
        
        // 验证必要字段
        if (!definition.id || !definition.name || !definition.method || !definition.url) {
          throw new Error('缺少必要字段：id, name, method, url');
        }

        const componentId = `api-caller-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        // 创建组件
        const ApiCallerComponent = () => React.createElement(ApiCaller, {
          definition,
          className: 'my-4'
        });

        componentInstances.set(componentId, ApiCallerComponent);

        return {
          type: 'api-caller',
          raw,
          definition,
          componentId
        };
      } catch (error) {
        console.error('解析API调用器定义失败:', error);
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        return {
          type: 'api-caller-error',
          raw,
          error: errorMessage
        };
      }
    }
  },
  renderer(token: any) {
    if (token.type === 'api-caller-error') {
      return `<div class="bg-red-50 border border-red-200 rounded p-4 my-4">
        <p class="text-red-800 font-medium">API调用器配置错误</p>
        <p class="text-red-600 text-sm">${token.error}</p>
      </div>`;
    }
    return `<div id="${token.componentId}" class="api-caller-placeholder"></div>`;
  }
};

// 渲染所有组件实例
export const renderComponents = (container: HTMLElement) => {
  // 渲染 API 表格组件
  const apiTablePlaceholders = container.querySelectorAll('.api-table-placeholder');
  apiTablePlaceholders.forEach((placeholder) => {
    const componentId = placeholder.id;
    const Component = componentInstances.get(componentId);
    if (Component && placeholder.parentNode) {
      const root = createRoot(placeholder);
      root.render(React.createElement(Component));
    }
  });

  // 渲染 API 调用器组件
  const apiCallerPlaceholders = container.querySelectorAll('.api-caller-placeholder');
  apiCallerPlaceholders.forEach((placeholder) => {
    const componentId = placeholder.id;
    const Component = componentInstances.get(componentId);
    if (Component && placeholder.parentNode) {
      const root = createRoot(placeholder);
      root.render(React.createElement(Component));
    }
  });
};

// 清理组件实例
export const clearComponents = () => {
  componentInstances.clear();
};