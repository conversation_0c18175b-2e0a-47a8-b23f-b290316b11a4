'use client';
import { createContext, useContext, useRef, useEffect, useState } from 'react';  
import StateStore from './store/StateStore';
 
const store = new StateStore();
const StateContext = createContext<StateStore | undefined>(undefined);
 
export function StateProvider({ children }: { children: React.ReactNode }) { 

  useEffect(() => {
    store.init();
  }, []);
 
  return (
    <StateContext.Provider value={store}>
      {children}
    </StateContext.Provider>
  );
}
 
export function useStateStore() {
  const context = useContext(StateContext);
  if (context === undefined) {
    throw new Error('useStateStore must be used within an StateProvider');
  }
  return context;
} 