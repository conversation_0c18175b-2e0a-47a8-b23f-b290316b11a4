'use client'
import React, { useState, useEffect } from 'react';
import { useStateStore } from '../../StateContext';
import { observer } from 'mobx-react-lite';
import { AppSettings, TenantEntry } from '../../types';
 

const ConfigModal: React.FC = () => {
  const appStore = useStateStore();
  const [settings, setSettings] = useState<AppSettings>({ ...appStore.settings });
  const [selectedTenant, setSelectedTenant] = useState<string>(appStore.tenantId || '');
  
  // 重置为当前状态
  useEffect(() => {
    if (appStore.showConfigModal) {
      setSettings({ ...appStore.settings });
      setSelectedTenant(appStore.tenantId || '');
    }
  }, [appStore.showConfigModal, appStore.settings, appStore.tenantId]);
  
  // 处理设置变更
  const handleSettingChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // 处理租户选择
  const handleTenantChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedTenant(e.target.value);
  };
  
  const handleClose = () => {
    appStore.setConfigModal(false);
  }
  
  const handleSave = () => {  
    appStore.updateSettings(settings);
    appStore.updateTenant(selectedTenant);
    handleClose();
  }; 
  
  if (!appStore.showConfigModal) return null;
  
  return (
    <div className="modal-overlay">
      <div className="modal-content config-modal">
        <div className="modal-header">
          <h5 className="modal-title">配置设置</h5>
          <button 
            type="button" 
            className="close-button"
            onClick={handleClose}
          >
            ×
          </button>
        </div>
        
        <div className="modal-body">
          <div className="config-section">
            <h6>环境设置</h6>
            <div className="form-group">
              <label htmlFor="environment">环境</label>
              <select
                id="environment"
                name="environment"
                className="form-control"
                value={settings.environment}
                onChange={handleSettingChange}
              >
                <option value="dev">开发环境</option>
                <option value="test">测试环境</option>
                <option value="uat">UAT环境</option>
              </select>
            </div>
            
            <div className="form-group">
              <label htmlFor="devEnv">开发环境URL</label>
              <input
                type="text"
                id="devEnv"
                name="devEnv"
                className="form-control"
                value={settings.devEnv}
                onChange={handleSettingChange}
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="testEnv">测试环境URL</label>
              <input
                type="text"
                id="testEnv"
                name="testEnv"
                className="form-control"
                value={settings.testEnv}
                onChange={handleSettingChange}
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="uatEnv">UAT环境URL</label>
              <input
                type="text"
                id="uatEnv"
                name="uatEnv"
                className="form-control"
                value={settings.uatEnv}
                onChange={handleSettingChange}
              />
            </div>
          </div>
          
          <div className="config-section">
            <h6>租户设置</h6>
            <div className="form-group">
              <label htmlFor="tenantId">选择租户</label>
              <select
                id="tenantId"
                name="tenantId"
                className="form-control"
                value={selectedTenant}
                onChange={handleTenantChange}
              >
                <option value="">-- 选择租户 --</option>
                {appStore.tenantList.map((tenant: TenantEntry) => (
                  <option key={tenant.tenantId} value={tenant.tenantId}>
                    {tenant.tenantName} ({tenant.tenantId})
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="config-section">
            <h6>应用设置</h6>
            <div className="form-group">
              <label htmlFor="currentLanguage">语言</label>
              <select
                id="currentLanguage"
                name="currentLanguage"
                className="form-control"
                value={settings.currentLanguage}
                onChange={handleSettingChange}
              >
                <option value="en">English</option>
                <option value="cn">中文</option>
              </select>
            </div>
            
            <div className="form-group">
              <label htmlFor="operationMode">操作模式</label>
              <select
                id="operationMode"
                name="operationMode"
                className="form-control"
                value={settings.operationMode}
                onChange={handleSettingChange}
              >
                <option value="auto">普通用户</option>
                <option value="manual">管理员</option>
              </select>
            </div>
            
            <div className="form-group">
              <label htmlFor="renderFormat">渲染格式</label>
              <select
                id="renderFormat"
                name="renderFormat"
                className="form-control"
                value={settings.renderFormat}
                onChange={handleSettingChange}
              >
                <option value="markdown">Markdown</option>
                <option value="mindmap">思维导图</option>
                <option value="tree">树形结构</option>
              </select>
            </div>
            
            <div className="form-group">
              <label htmlFor="reportType">报告类型</label>
              <select
                id="reportType"
                name="reportType"
                className="form-control"
                value={settings.reportType}
                onChange={handleSettingChange}
              >
                <option value="initial">首次报告</option>
                <option value="follow-up">随访报告</option>
              </select>
            </div>
          </div>
        </div>
        
        <div className="modal-footer">
          <button 
            type="button" 
            className="btn btn-secondary"
            onClick={handleClose}
          >
            取消
          </button>
          
          <button 
            type="button" 
            className="btn btn-primary"
            onClick={handleSave}
          >
            保存
          </button>
        </div>
      </div>
    </div>
  );
};

 
export default observer(ConfigModal);