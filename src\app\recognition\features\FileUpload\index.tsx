'use client'
import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { useStateStore } from '../../StateContext'; 
import { observer } from 'mobx-react-lite'; 
 

const FileUpload: React.FC = () => {
  const appStore = useStateStore();
  const [isDragging, setIsDragging] = useState(false);
 
    
  const handleFileUpload = useCallback(async (file: File) => {
    await appStore.fileStore.uploadFile(file); 
  }, [appStore.fileStore]);
  
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      handleFileUpload(acceptedFiles[0]);
    }
  }, [handleFileUpload]);
  
  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    maxFiles: 1,
    accept: {
      'application/pdf': ['.pdf'],
      'image/*': ['.jpg', '.jpeg', '.png']
    },
    onDragEnter: () => setIsDragging(true),
    onDragLeave: () => setIsDragging(false)
  });
  
  return (
    <div className="upload-section">
      <h2>文件上传</h2>
      {!appStore.fileStore.file && (
      <div 
        {...getRootProps()} 
        className={`dropzone ${isDragging ? 'dropzone-active' : ''}`}
      >
        <input {...getInputProps()} />
        <p>拖放文件到此处，或点击选择文件</p>
        <p className="small-text">支持PDF文件和图片文件</p>
      </div>
      )}
      {appStore.fileStore.file && (
        <div className="file-info">
          <p>{appStore.fileStore.file.name}</p>
          <button className='link-btn' onClick={() => appStore.fileStore.removeFile() }>重新上传</button>
        </div>
      )} 
    </div>
  );
};

export default observer(FileUpload);