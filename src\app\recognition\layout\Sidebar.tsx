'use client'
import React, { useCallback } from 'react';
import { useStateStore } from '../StateContext';
import { observer } from 'mobx-react-lite';
import { FaLanguage, FaChartPie, FaEdit, FaDatabase, FaSync, FaCog, FaRedo, FaFileExport } from 'react-icons/fa';
import useThrottleClick from "@/hooks/useThrottleClick";
import { message } from "@/utils/message"; // Import the new message utility
 
const Sidebar: React.FC = () => {
  const appStore = useStateStore();
   
  const isVisible = (buttonType: 'narrative' | 'transform') => {
    if (appStore.settings.operationMode === 'auto') { 
      if (buttonType === 'narrative' || buttonType === 'transform') {
        return false;
      }
    }
    return true;
  };

  const handleGenerateInsightView = useThrottleClick(async () => { 
  });
 
  const handleGenerateEventDescription = useThrottleClick(async () => { 
  });
  
  const handleStructuredDataClick = useThrottleClick(async () => { 
  }); 

  const handleSecondaryTransformClick = useThrottleClick(async () => { 
  });
   
  const handleConfigClick = useCallback(() => {
    appStore.setConfigModal(true);
  }, [appStore]);
   
  const handleResetClick = useCallback(() => {
    if (confirm('您确定要重置所有数据吗？')) {
      localStorage.clear();
      sessionStorage.clear();
      window.location.reload();
    }
  }, []);
   
  const handleExportClick = useCallback(() => { 
    appStore.setExportModal(true); 
  }, [appStore]);
  

  const handleLanguageToggle = useCallback(() => {
      appStore.switchLanguage();
      message.success(`语言已切换为 ${appStore.settings.currentLanguage === 'en' ? 'English' : '中文'}`);
    }, [appStore]);

  return (
    <div className="sidebar">
      {/* Replaced text placeholders with react-icons */}
      <button title="Switch Language" onClick={handleLanguageToggle} className="p-2 hover:bg-gray-200 rounded">
        <FaLanguage size={20} />
      </button>

      <button id="generateInsightView" title="Generate Insight View" onClick={handleGenerateInsightView} className="p-2 hover:bg-gray-200 rounded">
        <FaChartPie size={20} />
      </button>

      {isVisible('narrative') && (
        <button id="generateEventDescription" title="Generate Event Description" onClick={handleGenerateEventDescription} className="p-2 hover:bg-gray-200 rounded">
          <FaEdit size={20} />
        </button>
      )}

      <button id="generateStructuredData" title="Generate Structured Data" onClick={handleStructuredDataClick} className="p-2 hover:bg-gray-200 rounded">
        <FaDatabase size={20} />
      </button>

      {isVisible('transform') && (
        <button id="secondaryTransform" title="Secondary Transformation" onClick={handleSecondaryTransformClick} className="p-2 hover:bg-gray-200 rounded">
          <FaSync size={20} />
        </button>
      )}

      <button id="configButton" title="Configuration" onClick={handleConfigClick} className="p-2 hover:bg-gray-200 rounded">
        <FaCog size={20} />
      </button>

      <button id="resetButton" title="Reset Data" onClick={handleResetClick} className="p-2 hover:bg-gray-200 rounded">
        <FaRedo size={20} />
      </button>

      <button id="exportButton" title="Export Results" onClick={handleExportClick} className="p-2 hover:bg-gray-200 rounded">
        <FaFileExport size={20} />
      </button>
    </div>
  );
};

export default observer(Sidebar);