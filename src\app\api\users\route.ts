import { NextRequest, NextResponse } from 'next/server';

// 标准化响应格式
function createStandardResponse(data: any, code: number = 10000, message: string = '') {
  return { data, code, message };
}

// 模拟用户数据库
const usersDatabase = [
  { id: '1', name: '张三', email: '<EMAIL>', role: 'admin', status: '活跃', createTime: '2024-01-15' },
  { id: '2', name: '李四', email: '<EMAIL>', role: 'user', status: '活跃', createTime: '2024-02-20' },
  { id: '3', name: '王五', email: '<EMAIL>', role: 'user', status: '待激活', createTime: '2024-03-10' },
  { id: '4', name: '赵六', email: '<EMAIL>', role: 'guest', status: '活跃', createTime: '2024-04-05' },
  { id: '5', name: '钱七', email: '<EMAIL>', role: 'user', status: '禁用', createTime: '2024-05-12' },
  { id: '6', name: '孙八', email: '<EMAIL>', role: 'admin', status: '活跃', createTime: '2024-06-01' }
];

const headers = [
  { name: '用户ID', key: 'id' },
  { name: '姓名', key: 'name' },
  { name: '邮箱', key: 'email' },
  { name: '角色', key: 'role' },
  { name: '状态', key: 'status' },
  { name: '创建时间', key: 'createTime' }
];

// 搜索和过滤功能
function filterUsers(users: any[], searchTerm: string) {
  if (!searchTerm) return users;
  const searchFields = ['name', 'email', 'role', 'status'];
  return users.filter(user => 
    searchFields.some(field => 
      String(user[field] || '').toLowerCase().includes(searchTerm.toLowerCase())
    )
  );
}

// 排序功能
function sortUsers(users: any[], sortBy: string) {
  if (!sortBy) return users;
  const [field, direction] = sortBy.split(':');
  if (!field || !direction) return users;
  
  return [...users].sort((a, b) => {
    const aVal = a[field];
    const bVal = b[field];
    
    // 数字排序（如ID）
    if (!isNaN(aVal) && !isNaN(bVal)) {
      return direction === 'ASC' ? aVal - bVal : bVal - aVal;
    }
    
    // 字符串排序
    const aStr = String(aVal || '').toLowerCase();
    const bStr = String(bVal || '').toLowerCase();
    
    if (direction === 'ASC') {
      return aStr.localeCompare(bStr);
    } else {
      return bStr.localeCompare(aStr);
    }
  });
}

// 分页功能
function paginateUsers(users: any[], page: number, pageSize: number) {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return {
    items: users.slice(startIndex, endIndex),
    total: users.length
  };
}

export async function GET(request: NextRequest) {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  const { searchParams } = new URL(request.url);
  const search = searchParams.get('search') || '';
  const sortBy = searchParams.get('sortBy') || '';
  const page = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('pageSize') || '10');

  try {
    // 应用搜索过滤
    let filteredUsers = filterUsers(usersDatabase, search);

    // 应用排序
    filteredUsers = sortUsers(filteredUsers, sortBy);

    // 应用分页
    const { items: paginatedUsers, total } = paginateUsers(filteredUsers, page, pageSize);

    // 构建响应数据
    const responseData = {
      headers,
      rows: paginatedUsers,
      pagination: {
        total,
        page,
        pageSize
      },
      sortBy: sortBy || undefined
    };

    return NextResponse.json(createStandardResponse(responseData));

  } catch (error) {
    return NextResponse.json(
      createStandardResponse(null, 50000, '服务器内部错误'),
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  try {
    const body = await request.json();
    const { name, email, role } = body;

    // 验证必填字段
    if (!name || !email || !role) {
      return NextResponse.json(
        createStandardResponse(null, 40001, '姓名、邮箱和角色都是必填项'),
        { status: 400 }
      );
    }

    // 检查邮箱是否已存在
    if (usersDatabase.some(user => user.email === email)) {
      return NextResponse.json(
        createStandardResponse(null, 40002, '邮箱地址已存在'),
        { status: 400 }
      );
    }

    // 创建新用户
    const newUser = {
      id: (usersDatabase.length + 1).toString(),
      name,
      email,
      role,
      status: '活跃',
      createTime: new Date().toISOString().split('T')[0]
    };

    usersDatabase.push(newUser);

    return NextResponse.json(
      createStandardResponse(
        { user: newUser },
        10000,
        `用户 ${name} 创建成功`
      )
    );

  } catch (error) {
    return NextResponse.json(
      createStandardResponse(null, 50000, '服务器内部错误'),
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  try {
    const body = await request.json();
    const { id, newName, newEmail } = body;

    // 验证ID
    if (!id) {
      return NextResponse.json(
        createStandardResponse(null, 40001, '用户ID是必填项'),
        { status: 400 }
      );
    }

    // 查找用户
    const userIndex = usersDatabase.findIndex(user => user.id === id);
    if (userIndex === -1) {
      return NextResponse.json(
        createStandardResponse(null, 40401, '用户不存在'),
        { status: 404 }
      );
    }

    const user = usersDatabase[userIndex];
    const oldData = { ...user };

    // 更新用户信息
    if (newName) {
      user.name = newName;
    }
    if (newEmail) {
      // 检查新邮箱是否已存在（排除当前用户）
      if (usersDatabase.some(u => u.email === newEmail && u.id !== id)) {
        return NextResponse.json(
          createStandardResponse(null, 40002, '邮箱地址已存在'),
          { status: 400 }
        );
      }
      user.email = newEmail;
    }

    return NextResponse.json(
      createStandardResponse(
        { 
          user: user,
          oldData: oldData 
        },
        10000,
        `用户 ${user.name} 信息更新成功`
      )
    );

  } catch (error) {
    return NextResponse.json(
      createStandardResponse(null, 50000, '服务器内部错误'),
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  try {
    const body = await request.json();
    const { id, confirm } = body;

    // 验证参数
    if (!id) {
      return NextResponse.json(
        createStandardResponse(null, 40001, '用户ID是必填项'),
        { status: 400 }
      );
    }

    if (!confirm) {
      return NextResponse.json(
        createStandardResponse(null, 40003, '请确认删除操作'),
        { status: 400 }
      );
    }

    // 查找用户
    const userIndex = usersDatabase.findIndex(user => user.id === id);
    if (userIndex === -1) {
      return NextResponse.json(
        createStandardResponse(null, 40401, '用户不存在'),
        { status: 404 }
      );
    }

    const user = usersDatabase[userIndex];

    // 检查是否为管理员（不允许删除）
    if (user.role === 'admin') {
      return NextResponse.json(
        createStandardResponse(null, 40003, '不能删除管理员用户'),
        { status: 400 }
      );
    }

    // 删除用户
    usersDatabase.splice(userIndex, 1);

    return NextResponse.json(
      createStandardResponse(
        { deletedUser: user },
        10000,
        `用户 ${user.name} 删除成功`
      )
    );

  } catch (error) {
    return NextResponse.json(
      createStandardResponse(null, 50000, '服务器内部错误'),
      { status: 500 }
    );
  }
}