import { NextRequest, NextResponse } from 'next/server';

// 标准化响应格式
function createStandardResponse(data: any, code: number = 10000, message: string = '') {
  return { data, code, message };
}

// 搜索和过滤功能
function filterData(data: any[], searchTerm: string, fields: string[]) {
  if (!searchTerm) return data;
  return data.filter(item =>
    fields.some(field =>
      String(item[field] || '').toLowerCase().includes(searchTerm.toLowerCase())
    )
  );
}

// 排序功能
function sortData(data: any[], sortBy: string) {
  if (!sortBy) return data;
  const [field, direction] = sortBy.split(':');
  if (!field || !direction) return data;
  
  return [...data].sort((a, b) => {
    const aVal = a[field];
    const bVal = b[field];
    
    // 数字排序
    if (!isNaN(aVal) && !isNaN(bVal)) {
      return direction === 'ASC' ? aVal - bVal : bVal - aVal;
    }
    
    // 字符串排序
    const aStr = String(aVal || '').toLowerCase();
    const bStr = String(bVal || '').toLowerCase();
    
    if (direction === 'ASC') {
      return aStr.localeCompare(bStr);
    } else {
      return bStr.localeCompare(aStr);
    }
  });
}

// 分页功能
function paginateData(data: any[], page: number, pageSize: number) {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return {
    items: data.slice(startIndex, endIndex),
    total: data.length
  };
}

// 测试用的数据源
const baseTestData = {
  simple: [
    { id: '1', name: '张三', age: 25, department: '开发部' },
    { id: '2', name: '李四', age: 30, department: '测试部' },
    { id: '3', name: '王五', age: 28, department: '产品部' },
    { id: '4', name: '赵六', age: 32, department: '设计部' }
  ],
  
  products: [
    { id: 1, name: 'iPhone 15', price: 5999, category: '手机', stock: 50, rating: 4.8 },
    { id: 2, name: 'MacBook Pro', price: 12999, category: '笔记本', stock: 30, rating: 4.9 },
    { id: 3, name: 'iPad Air', price: 4399, category: '平板', stock: 80, rating: 4.7 },
    { id: 4, name: 'AirPods Pro', price: 1899, category: '耳机', stock: 120, rating: 4.6 },
    { id: 5, name: 'Apple Watch', price: 2599, category: '智能手表', stock: 60, rating: 4.5 },
    { id: 6, name: 'Magic Mouse', price: 599, category: '配件', stock: 200, rating: 4.3 }
  ],
  
  users: [
    { id: 1, name: '张三', email: '<EMAIL>', role: 'admin', status: '活跃', createTime: '2024-01-15' },
    { id: 2, name: '李四', email: '<EMAIL>', role: 'user', status: '活跃', createTime: '2024-02-20' },
    { id: 3, name: '王五', email: '<EMAIL>', role: 'user', status: '待激活', createTime: '2024-03-10' },
    { id: 4, name: '赵六', email: '<EMAIL>', role: 'guest', status: '活跃', createTime: '2024-04-05' },
    { id: 5, name: '钱七', email: '<EMAIL>', role: 'user', status: '禁用', createTime: '2024-05-12' }
  ],
  
  stats: [
    { metric: '总用户数', value: 10523, change: '+12%', trend: 'up' },
    { metric: '活跃用户', value: 8745, change: '+8%', trend: 'up' },
    { metric: '收入', value: 156789, change: '+15%', trend: 'up' },
    { metric: '订单数', value: 2341, change: '+5%', trend: 'up' }
  ],
  
  large: Array.from({ length: 100 }, (_, i) => ({
    id: i + 1,
    name: `产品${i + 1}`,
    price: Math.floor(Math.random() * 10000) + 100,
    stock: Math.floor(Math.random() * 1000) + 10,
    sales: Math.floor(Math.random() * 500) + 1,
    rating: Number((Math.random() * 2 + 3).toFixed(1)),
    category: ['电子产品', '服装', '食品', '图书', '家居'][Math.floor(Math.random() * 5)],
    status: ['在售', '缺货', '下架'][Math.floor(Math.random() * 3)]
  }))
};

// 数据类型到表头的映射
const dataHeaders = {
  simple: [
    { name: 'ID', key: 'id' },
    { name: '姓名', key: 'name' },
    { name: '年龄', key: 'age' },
    { name: '部门', key: 'department' }
  ],
  products: [
    { name: '产品ID', key: 'id' },
    { name: '产品名称', key: 'name' },
    { name: '价格', key: 'price' },
    { name: '分类', key: 'category' },
    { name: '库存', key: 'stock' },
    { name: '评分', key: 'rating' }
  ],
  users: [
    { name: '用户ID', key: 'id' },
    { name: '姓名', key: 'name' },
    { name: '邮箱', key: 'email' },
    { name: '角色', key: 'role' },
    { name: '状态', key: 'status' },
    { name: '创建时间', key: 'createTime' }
  ],
  stats: [
    { name: '指标', key: 'metric' },
    { name: '数值', key: 'value' },
    { name: '变化', key: 'change' },
    { name: '趋势', key: 'trend' }
  ],
  large: [
    { name: '序号', key: 'id' },
    { name: '产品名称', key: 'name' },
    { name: '价格', key: 'price' },
    { name: '库存', key: 'stock' },
    { name: '销量', key: 'sales' },
    { name: '评分', key: 'rating' },
    { name: '类别', key: 'category' },
    { name: '状态', key: 'status' }
  ]
};

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const type = searchParams.get('type') || 'simple';
  const delay = parseInt(searchParams.get('delay') || '0');
  
  // 增强表格参数
  const search = searchParams.get('search') || '';
  const sortBy = searchParams.get('sortBy') || '';
  const page = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('pageSize') || '10');
  
  // 模拟延迟
  if (delay > 0) {
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  try {
    // 错误和空数据的特殊处理
    if (type === 'error') {
      return NextResponse.json(
        createStandardResponse(null, 50001, '这是一个测试错误'),
        { status: 500 }
      );
    }
    
    if (type === 'empty') {
      return NextResponse.json(
        createStandardResponse({
          headers: [],
          rows: [],
          pagination: { total: 0, page: 1, pageSize: 10 },
          sortBy: ''
        })
      );
    }

    // 获取数据源和表头
    const dataSource = baseTestData[type as keyof typeof baseTestData];
    const headers = dataHeaders[type as keyof typeof dataHeaders];
    
    if (!dataSource || !headers) {
      return NextResponse.json(
        createStandardResponse(null, 40001, '无效的数据类型'),
        { status: 400 }
      );
    }

    // 应用搜索过滤
    let filteredData = [...dataSource];
    if (search) {
      const searchFields = headers.map(h => h.key);
      filteredData = filterData(filteredData, search, searchFields);
    }

    // 应用排序
    if (sortBy) {
      filteredData = sortData(filteredData, sortBy);
    }

    // 应用分页
    const { items: paginatedData, total } = paginateData(filteredData, page, pageSize);

    // 构建响应数据
    const responseData = {
      headers,
      rows: paginatedData,
      pagination: {
        total,
        page,
        pageSize
      },
      sortBy: sortBy || undefined
    };

    return NextResponse.json(createStandardResponse(responseData));

  } catch (error) {
    return NextResponse.json(
      createStandardResponse(null, 50000, '服务器内部错误'),
      { status: 500 }
    );
  }
}

// 支持 POST 请求用于更复杂的测试
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 构建响应数据
    const responseData = {
      userId: Math.floor(Math.random() * 10000).toString(),
      userName: body.name || '新用户',
      submittedData: body,
      created: true,
      timestamp: new Date().toISOString()
    };
    
    return NextResponse.json(
      createStandardResponse(responseData, 10000, '数据提交成功')
    );
  } catch (error) {
    return NextResponse.json(
      createStandardResponse(null, 40002, '请求处理失败'),
      { status: 400 }
    );
  }
}