import React, { useMemo, useState } from "react";
import { FaAngleUp, FaAngleDown } from "react-icons/fa";
import { ListRenderer } from "./ListRenderer";
import { ObjectRenderer } from "./ObjectRenderer";
import { ValueRenderer } from "./ValueRenderer";



const ListFieldsRenderer: React.FC<TableRendererProps> = ({ path, data }) => {
  return data.map((row, rowIndex) => (
    <div key={`${path}.${rowIndex}`} >
      <ObjectRenderer path={`${path}.${rowIndex}`} grids={Math.ceil(Object.keys(row).length / 3)} data={row} />
      {rowIndex < data.length - 1 && (
        <div className="border-b border-blue-500 my-4"></div>
      )}
    </div>
  ))
}


interface TableRendererProps {
  path: string;
  data: any[];
}

export const TableRenderer: React.FC<TableRendererProps> = ({ path, data }) => {
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: "ascending" | "descending" } | null>(null);
 
  const getValue = (row:any, key:string) => {
    const v = row[key];
    return v; 
  }

  const sortedData = useMemo(() => {
    if (!data || data.length === 0) return [];
    const sortableItems = [...data.map((row, i) => {
      return {...row, _i : i}
    })];
    if (sortConfig !== null) { 
      sortableItems.sort((a, b) => { 
        if (getValue(a,sortConfig.key) < getValue(b,sortConfig.key)) {
          return sortConfig.direction === "ascending" ? -1 : 1;
        }
        if (getValue(a,sortConfig.key) > getValue(b,sortConfig.key)) {
          return sortConfig.direction === "ascending" ? 1 : -1;
        }
        return 0;
      });
    } 
    return sortableItems;
  }, [data, sortConfig]);

  if (data.length === 0) {
    return <div className="text-gray-400">No data</div>;
  }
 
  

  if (/_\d+_/.test(Object.keys(data[0]).join(','))) {
    return <ListFieldsRenderer path={path} data={data} />;
  }

  const cols = data.map(row => Object.keys(row)).sort((a,b) => b.length - a.length)[0].filter((key) => key !== '报告分类' && key !== '报告模块');
  
  const columns = cols.filter((key) => {
    const row = data.find((row) => row[key] !== null && row[key] !== undefined);   
    if(!row) return true;
    const value = row[key];
    return typeof value !== "object";
  });

  const expandColumns = cols.filter((key) => {
    const row = data.find((row) => row[key] !== null && row[key] !== undefined);
    if(!row) return false;
    const value = row[key];
    return typeof value === "object";
  });

  const requestSort = (key: string) => {
    let direction: "ascending" | "descending" = "descending";
    if (sortConfig && sortConfig.key === key && sortConfig.direction === "descending") {
      direction = "ascending";
    }
    setSortConfig({ key, direction });
  };

  const getSortIndicator = (columnKey: string) => {
    if (!sortConfig || sortConfig.key !== columnKey) {
      return null;  
    }
    return sortConfig.direction === "ascending" ? <FaAngleUp className="inline" /> : <FaAngleDown className="inline" />;
  };

  return (
    <div className="overflow-x-auto" data-path={`${path}`}>
      <table className="min-w-full border-collapse">
        <thead className="bg-blue-50 sticky top-0">
          <tr>
            <th
              className="min-w-[10px] px-4 py-2 text-left whitespace-nowrap text-sm font-medium text-blue-700 border-b border-blue-200"
            >
              序号
            </th>
            {columns.map((column) => (
              <th
                key={column}
                onClick={() => requestSort(column)}
                className="relative min-w-[120px] px-4 py-2 text-left text-sm font-medium text-blue-700 border-b border-blue-200 cursor-pointer"
              >
                <span>
                  {getSortIndicator(column)}
                  {column.toString()}
                </span>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {sortedData.map((row, rowIndex) => (
            <React.Fragment key={rowIndex}>
              <tr className="hover:bg-blue-50">
                <td className="min-w-[10px] px-4 py-2 border-b border-gray-100">
                  {rowIndex + 1}
                </td>
                {columns.map((column) => (
                  <td
                    key={column}
                    className="min-w-[120px] px-4 py-2 border-b border-gray-100"
                  >
                    <ValueRenderer path={`${path}.${row._i}.${column}`} value={getValue(row,column)} />
                  </td>
                ))}
              </tr>
              {expandColumns.map(column => (
                row[column] && 
                <tr key={`${rowIndex}-${column}-expanded`}>
                  <td colSpan={columns.length + 1} className="px-4 py-2">
                    <div className="ml-4 mt-2 border-l-2 border-blue-200 pl-4">
                      <div className="text-sm font-bold text-blue-700 mb-1">{column}</div>
                      {Array.isArray(row[column]) ? (
                        row[column].length > 0 && typeof row[column][0] !== "object"
                          ? (<ListRenderer path={`${path}.${row._i}.${column}`} data={row[column]} />)
                          : (<TableRenderer path={`${path}.${row._i}.${column}`} data={row[column]} />)
                      ) : (
                        row[column] ? <ObjectRenderer path={`${path}.${row._i}.${column}`} grids={Math.ceil(columns.length / 3)} data={row[column]} /> : null
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  );
};
