import { Plugin, Command, ButtonView } from "ckeditor5";
import type { Writer, RootElement, Position } from "ckeditor5";

export default class AddFeedbackPlugin extends Plugin {
  static get pluginName() {
    return "AddFeedback";
  }

  init() {
    const editor = this.editor;

    // 定义命令
    editor.commands.add("addFeedback", new AddFeedbackCommand(editor));

    // 添加工具栏按钮
    editor.ui.componentFactory.add("addFeedback", (locale) => {
      const command = editor.commands.get("addFeedback")!;
      const buttonView = new ButtonView(locale);

      buttonView.set({
        label: "追加回复",
        icon: `<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path fill="currentColor" d="M18 4v12a2 2 0 01-2 2H4a2 2 0 01-2-2V4a2 2 0 012-2h12a2 2 0 012 2zM5 8h10M5 12h6M10 14l3-3-3-3v2H7v2h3v2z"/>
        </svg>`,
        tooltip: true,
        withText: false,
      });

      // 绑定命令状态
      buttonView.bind("isEnabled").to(command, "isEnabled");

      // 绑定点击事件
      buttonView.on("execute", () => {
        editor.execute("addFeedback");
      });

      return buttonView;
    });
  }
}

class AddFeedbackCommand extends Command {
  execute() {
    const editor = this.editor;
    const model = editor.model;
    const selection = model.document.selection;

    // 生成回复模板
    const currentTime = new Date().toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });

    const feedbackTemplate = `


## 问题回复 - ${currentTime}

**状态更新**: 问题已处理

**回复内容**:
<!-- 请在此处添加您的回复内容 -->


**处理人**: <!-- 请填写处理人姓名 -->

**备注**: <!-- 可选：添加其他备注信息 -->

---
`;

    model.change((writer: Writer) => {
      // 获取当前光标位置
      const currentPosition = selection.getFirstPosition();
      if (!currentPosition) return;
      const root = model.document.getRoot();
      if (!currentPosition || !root) return;

      // 创建从当前位置到文档末尾的范围
      const range = writer.createRange(
        currentPosition,
        writer.createPositionAt(root, "end")
      );

      let hrElement = null;

      // 遍历范围内的所有元素
      for (const value of range.getWalker()) {
        if (value.item.is("element", "horizontalLine")) {
          hrElement = value.item;
          break;
        }
      }

      if (hrElement) {
        // 在 hr 后插入模板
        const insertPosition = writer.createPositionAfter(hrElement);  

        // 使用 Markdown 处理器将文本转换为模型结构
        const markdownProcessor = editor.data.processor;
        const viewFragment = markdownProcessor.toView(feedbackTemplate);
        const modelFragment = editor.data.toModel(viewFragment);

        // 在找到的位置插入转换后的模型片段
        writer.insert(modelFragment, insertPosition);
      }
    });
  }

  refresh() {
    // 命令始终可用
    this.isEnabled = true;
  }
}
