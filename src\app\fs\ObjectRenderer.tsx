import React from "react";
import { ListRenderer } from "./ListRenderer";
import { TableRenderer } from "./TableRenderer";
import { ValueRenderer } from "./ValueRenderer";

export interface ObjectRendererProps {
  path: string;
  data: Record<string, any>;
  topLevelKeysForNav?: string[];
  grids?: number;
}

export const ObjectRenderer: React.FC<ObjectRendererProps> = ({ path, data, topLevelKeysForNav, grids = 3 }) => {
  const fields: string[] = [];
  const objectKeys: string[] = [];
  const listKeys: string[] = [];
  const tableKeys: string[] = [];
  
  Object.entries(data).map(([key, value]) => {
    if(key === '报告分类' || key === '报告模块'){ 
      return;
    }
    if(typeof value === "object" && value !== null){
      if(Array.isArray(value) && value.length > 0 && typeof value[0] !== "object"){
        listKeys.push(key);
      }else if(Array.isArray(value)){
        tableKeys.push(key);
      }else{
        objectKeys.push(key);
      }
    }else{
      fields.push(key);
    }
  });
  
  let cls = `grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-3 gap-4 mx-auto`
  if(fields.length <= 1){
    cls = `grid grid-cols-1 gap-4 mx-auto`
  }

  return (
    <>
      {fields.length > 0 && (
        <div className={cls} style={grids !== 3 ? { gridTemplateColumns: `repeat(${grids}, minmax(0, 1fr))` }: {}}>
        {fields.map((key) => 
          <div key={key} data-path={`${path}${key}`} className="p-1 bg-white shadow-sm">
            <div className="text-sm font-medium text-blue-600 mb-1">{key}</div>
            <ValueRenderer path={`${path}${key}`} value={data[key]} />
          </div>
        )}
        </div>
      )}

      {objectKeys.length > 0 && objectKeys.map((key) => (
        <div data-path={`${path}${key}`} key={key} id={topLevelKeysForNav?.includes(key) ? `section-${key}` : undefined} className="p-4 bg-white shadow-sm scroll-mt-16">
          <div className="text-sm font-bold text-blue-700 mb-1">{key}</div>
          <ObjectRenderer path={`${path}${key}.`} data={data[key]} /> 
        </div>
      ))}

      {listKeys.length > 0 && listKeys.map((key) => (
        <div data-path={`${path}${key}`} key={key} id={topLevelKeysForNav?.includes(key) ? `section-${key}` : undefined} className="p-4 bg-white shadow-sm mb-4 scroll-mt-16">
          <div className="text-sm font-medium text-blue-600 mb-1">{key}</div>
          <ListRenderer path={`${path}${key}.`} data={data[key]} />
        </div>
      ))}
 
      {tableKeys.length > 0 && tableKeys.map((key) => (
        <div data-path={`${path}${key}`} key={key} id={topLevelKeysForNav?.includes(key) ? `section-${key}` : undefined} className="p-4 bg-white shadow-sm mb-4 scroll-mt-16">
          <div className="text-sm font-medium text-blue-600 mb-1">{key}</div>
          <TableRenderer path={`${path}${key}`} data={data[key]} />
        </div>
      ))}
    </>
  );
};
