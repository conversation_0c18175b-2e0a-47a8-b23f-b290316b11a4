import type { FileInfo, PreviewFileInfo, DirectoryResponse, DIR } from '@/utils/fs'; 

export const STORAGE_KEY = 'fs_api_env';

export const ENV_CONFIG = {
  devEnv: "/",
  testEnv: "https://copilot-test.pharmaronclinical.com/pv-manus-front/",
  uatEnv: "https://copilot-uat.pharmaronclinical.com/pv-manus-front/"
};

export type EnvType = keyof typeof ENV_CONFIG; 

class FileSystemAPI {
  // baseUrl = ENV_CONFIG['devEnv']; 
  baseUrl = ''; 
  
  setEnv(env: EnvType){
    // this.baseUrl = ENV_CONFIG[env];
    // localStorage.setItem(STORAGE_KEY, env);
  }

  getEnv(){
    return (localStorage.getItem(STORAGE_KEY) || 'devEnv') as EnvType;
  }

  /**
   * 获取目录列表
   * @param subdir - 子目录路径
   * @param search - 搜索关键词（可选）
   */
  async getDirectoryList(type: DIR, subdir: string, search?: string): Promise<DirectoryResponse> {
    const params = new URLSearchParams();
    params.append('type', type);
    if (subdir) params.append('subdir', subdir);
    if (search) params.append('search', search);

    const response = await fetch(`${this.baseUrl}api/fs/list?${params.toString()}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  /**
   * 获取文件预览URL
   * @param filePath - 文件路径
   */
  getPreviewUrl(type: DIR, filePath: string): string {
    return `${this.baseUrl}api/fs/preview?type=${type}&file_path=${encodeURIComponent(filePath)}`;
  }

  /**
   * 获取文件下载URL
   * @param filePath - 文件路径
   */
  getDownloadUrl(type: DIR, filePath: string): string {
    return `${this.baseUrl}api/fs/download?type=${type}&file_path=${encodeURIComponent(filePath)}`;
  }

  
  async uploadFile(type: DIR, filePath: string, content: string)  {
    const formData = new FormData();
    formData.append('type', type);
    formData.append('path', filePath);
    formData.append('content', content);
    const response = await fetch(`${this.baseUrl}api/fs/upload`,{
      method: 'POST',
      body: formData,
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  /**
   * 获取文件内容
   * @param type - 目录类型
   * @param filePath - 文件路径
   */
  async getFileContent(type: DIR, filePath: string): Promise<string> {
    const response = await fetch(this.getPreviewUrl(type, filePath));
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.text();
  }

  /**
   * 删除文件或文件夹
   * @param type - 目录类型
   * @param filePath - 文件路径
   * @param password - 管理员密码
   */
  async deleteFile(type: DIR, filePath: string, password: string) {
    const response = await fetch(`${this.baseUrl}api/fs/delete`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type,
        filePath,
        password
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }
}

// 导出单例实例
export const fileSystemAPI = new FileSystemAPI();

// 导出类以便需要时可以创建新实例（比如测试时）
export default FileSystemAPI;