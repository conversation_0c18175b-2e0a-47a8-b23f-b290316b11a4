import { NextRequest, NextResponse } from "next/server";
import { download, dirDownload, DIR, getBaseDir } from "@/utils/fs";
import fs from "fs/promises";
import path from "path";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const filePath = searchParams.get("file_path");
    const type = (searchParams.get("type") || "document") as DIR;

    if (!filePath) {
      return NextResponse.json({ error: "文件路径不能为空" }, { status: 400 });
    }

    // 获取完整路径
     const baseDir = getBaseDir(type);
    const fullPath = path.resolve(baseDir, filePath);

    // 检查是文件还是目录
    const stats = await fs.stat(fullPath);
    const isDirectory = stats.isDirectory();

    // 根据类型调用不同的下载函数
    const { fileBuffer, headers } = isDirectory 
      ? await dirDownload(type, filePath)
      : await download(type, filePath, false);

    return new NextResponse(fileBuffer, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error("文件下载失败:", error);
    return NextResponse.json(
      { error: (error as Error).message || '文件下载失败' },
      { status: 400 }
    );
  }
}
