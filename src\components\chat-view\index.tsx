"use client";
import { useState, useRef, useEffect } from "react";
import { FaPaperPlane, FaUpload } from "react-icons/fa";
import {
  uploadFileWithEventStream,
  generatePlainTextAdverseEventSummary,
} from "./upload";
import { showNotification, requestPermission } from '@/utils/notification'

interface Message {
  content: string;
  sender: "user" | "ai";
  timestamp: Date;
}

interface ChatInterfaceProps {
  loading: boolean;
}

const ChatInterface = (props: ChatInterfaceProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setMessages([
      {
        content: "你好！我是你的助手。需要我帮你什么忙吗？",
        sender: "ai",
        timestamp: new Date(),
      },
    ]);
    requestPermission();
  }, []);

  const sendMessage = async () => {
    if (!input.trim()) return;

    // 添加用户消息
    const userMessage: Message = {
      content: input,
      sender: "user",
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput("");
  };
  const addUserMessage = (str: string) => {
    const msg: Message = {
      content: str,
      sender: "user",
      timestamp: new Date(),
    };
    setMessages((prev) => [...prev, msg]);
  };
  const addMessage = (str: string, wait = 500): Promise<void> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const msg: Message = {
          content: str,
          sender: "ai",
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, msg]);
        resolve();
      }, wait);
    });
  };

  const handleFileUpload = async (file: File) => {
    addUserMessage(`我上传了文件:${file.name}`);
    await uploadFileWithEventStream("/api/pv/excel/upload", file, {
      extraData: {
        userId: "78a10301-bf51-4467-9ef2-b35c425a64a4",
      },
      onEvent: async (event) => {
        if (!event.data || event?.data?.type !== "message") return;
        const { target_data, file_name } = event.data;

        await addMessage(`接收到文件: ${file_name}`);

        const txt = generatePlainTextAdverseEventSummary(target_data);
        await addMessage(txt);
      },
      onComplete: async (result) => {
        console.log("result :>> ", result);
        showNotification('已完成导入',`请在Argus助手中点击“自动创建”或“自动录入”`)
        await addMessage(
          `已完成导入，请在Argus助手中点击“自动创建”或“自动录入”`,
          1000
        );
      },
      onError: (error) => {
        showNotification('发送错误',error.message);
        addMessage(`发送错误:${error.message}`);
      },
    });
    return false; // 阻止默认上传行为
  };

  // 自动滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  return (
    <div className="flex flex-col h-full">
      {/* 聊天头部 */}
      <div className="bg-white dark:bg-gray-800 p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold">AI 助手</h2>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          在这里输入你的指令，AI会帮你操作
        </p>
      </div>

      {/* 聊天消息区域 */}
      <div className="flex-1 overflow-y-auto p-4 bg-gray-50 dark:bg-gray-900">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`mb-4 flex ${
              message.sender === "user" ? "justify-end" : "justify-start"
            }`}
          >
            <div
              className={`max-w-[80%] p-3 rounded-lg ${
                message.sender === "user"
                  ? "bg-blue-500 text-white"
                  : "bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 border border-gray-200 dark:border-gray-700"
              }`}
            >
              <p className="whitespace-pre-wrap">{message.content}</p>
              <div
                className={`text-xs mt-1 ${
                  message.sender === "user" ? "text-blue-100" : "text-gray-400"
                }`}
              >
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      <div className="p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && sendMessage()}
            placeholder="输入消息..."
            className="flex-1 mr-2 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-blue-500"
          />
          <input
            type="file"
            id="file-upload"
            className="hidden"
            onChange={(e) => {
              if (e.target.files && e.target.files[0]) {
                handleFileUpload(e.target.files[0]);
              }
            }}
          />
          <label
            htmlFor="file-upload"
            className="mr-2 p-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
          >
            <FaUpload className="text-gray-600 dark:text-gray-300" />
          </label>
          <button
            onClick={sendMessage}
            disabled={!input.trim()}
            className="p-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 flex items-center justify-center"
          >
            <FaPaperPlane />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;

