'use client';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import './fs.css';
import FilePreviewModal from './FilePreviewModal';
import EnvSettingModal from './EnvSettingModal';
import DeleteConfirmModal from './DeleteConfirmModal';
import { fileSystemAPI } from './api';
import type { FileInfo, PreviewFileInfo, DIR } from '@/utils/fs';
import { FaFileAlt, FaImage, FaFilePdf, FaFolder, FaEye, FaDownload, FaFile, FaSort, FaSortUp, FaSortDown, FaArrowUp, FaHome, FaTrash } from 'react-icons/fa';
import { message } from '@/utils/message';


const FileSystemPage = () => {
  const router = useRouter(); // Added
  const searchParams = useSearchParams(); // Added
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [currentPath, setCurrentPath] = useState('/');
  const [isLoading, setIsLoading] = useState(false);
  const [previewFile, setPreviewFile] = useState<PreviewFileInfo | null>(null);
  const [isEnvSettingVisible, setIsEnvSettingVisible] = useState(false);
  const [deleteFile, setDeleteFile] = useState<FileInfo | null>(null);
  
  const [dirType, setDirType] = useState<DIR>('document');
  // State for sorting
  const [sortColumn, setSortColumn] = useState<keyof FileInfo | 'type' | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');


  const loadFiles = useCallback(async (dirType: DIR ,path: string) => {
    setIsLoading(true);
    try {
      const subdir = path[0] === '/' ? path.substring(1): path;
      const data = await fileSystemAPI.getDirectoryList(dirType, subdir);
      
      if (data.error) {
        throw new Error(data.error);
      }

      const mappedFiles = data.items.map((item: FileInfo) => ({
        ...item,
        type: item.is_dir ? 'directory' : getFileExtension(item.name)
      }));
      
      setFiles(mappedFiles);
      setCurrentPath(data.current_path);
    } catch (error) {
      console.error('加载文件失败:', error);
      message.error('加载文件失败');
    } finally {
      setIsLoading(false);
    }
  }, []); 
  useEffect(() => {
    const path = searchParams.get('path') || '/';
    const typeParam = searchParams.get('type');
    const previewPath = searchParams.get('preview');
    const newDirType = typeParam === 'report' ? 'report' : 'document';
    const _searchParams = new URLSearchParams(window.location.search);
    if(_searchParams.get('type') !== typeParam){
      return;
    }

    setDirType(newDirType);
    setCurrentPath(path); 
    
    // 加载当前目录文件
    loadFiles(newDirType, path).then(() => {
      // 处理预览文件参数
      if (previewPath) {
        // 从文件列表中查找匹配的文件
        const previewFilePath = decodeURIComponent(previewPath);
        const fileUrl = fileSystemAPI.getPreviewUrl(newDirType, previewFilePath);
        const fileName = previewFilePath.split('/').pop() || '';
        const fileType = getFileExtension(fileName);
        
        setPreviewFile({
          url: fileUrl,
          name: fileName,
          type: fileType
        });
      } else {
        // 如果没有预览参数，清除预览状态
        setPreviewFile(null);
      }
    });

  }, [searchParams, loadFiles]); 
  
  // useEffect(() => {
  //   fileSystemAPI.setEnv(fileSystemAPI.getEnv());
  //   loadFiles('/');
  // }, [loadFiles, dirType]);
 
  const getFileIcon = (fileType: string | undefined) => {
    const size = 18; // Icon size
    if (!fileType) return <FaFile size={size} />;

    switch (fileType.toLowerCase()) {
      case 'md':
      case 'txt':
        return <FaFileAlt size={size} />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <FaImage size={size} />;
      case 'pdf':
        return <FaFilePdf size={size} />;
      case 'directory':
        return <FaFolder size={size} />;
      default:
        return <FaFile size={size} />;
    }
  };

  const getFileExtension = (fileName: string): string => {
    const parts = fileName.split('.');
    return parts.length > 1 ? parts.pop()?.toLowerCase() || '' : '';
  };

  const handlePreview = (file: FileInfo) => {
    const fileUrl = fileSystemAPI.getPreviewUrl(dirType, file.path);
    const fileType = getFileExtension(file.name);
    setPreviewFile({
      url: fileUrl,
      name: file.name,
      type: fileType
    });
    
    // 更新URL中的path参数，添加preview参数
    router.push(`/fs?type=${dirType}&path=${encodeURIComponent(currentPath)}&preview=${encodeURIComponent(file.path)}`);
  };

  const handleDownload = (file: FileInfo) => {
    const downloadUrl = fileSystemAPI.getDownloadUrl(dirType, file.path);
    const a = document.createElement('a');
    a.href = downloadUrl;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleDelete = (file: FileInfo) => {
    setDeleteFile(file);
  };

  const handleDeleteConfirm = async (password: string) => {
    if (!deleteFile) return;

    const fileType = deleteFile.is_dir ? '文件夹' : '文件';

    try {
      await fileSystemAPI.deleteFile(dirType, deleteFile.path, password);
      message.success(`${fileType}删除成功`);
      // 刷新文件列表
      loadFiles(dirType, currentPath);
      setDeleteFile(null);
    } catch (error) {
      console.error('删除失败:', error);
      message.error((error as Error).message || '删除失败');
    }
  };

  const handleDeleteCancel = () => {
    setDeleteFile(null);
  };

  const navigateToDirectory = (dirName: string) => {
    const newPath = currentPath === '/'
      ? `/${dirName}`
      : `${currentPath}/${dirName}`;
    // setCurrentPath and loadFiles removed, handled by useEffect on URL change
    router.push(`/fs?type=${dirType}&path=${encodeURIComponent(newPath)}`); // Update URL
  };

  const navigateUp = () => {
    if (currentPath === '/') return;
    const newPath = currentPath.substring(0, currentPath.lastIndexOf('/'));
    const finalPath = newPath === '' ? '/' : newPath;
    router.push(`/fs?type=${dirType}&path=${encodeURIComponent(finalPath)}`);
  };

  // Sorting handler
  const handleSort = (column: keyof FileInfo | 'type') => {
    if (sortColumn === column) {
      setSortDirection(prevDirection => (prevDirection === 'asc' ? 'desc' : 'asc'));
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  // Memoized sorted files - useMemo should already be imported now
  const sortedFiles = useMemo(() => {
    if (!sortColumn) return files; // No sorting applied initially or if column is null

    return [...files].sort((a, b) => {
      // Always sort directories first
      if (a.is_dir && !b.is_dir) return -1;
      if (!a.is_dir && b.is_dir) return 1;

      let valA: any;
      let valB: any;

      if (sortColumn === 'type') {
        valA = a.is_dir ? '文件夹' : (a.type ? a.type.toUpperCase() : '未知');
        valB = b.is_dir ? '文件夹' : (b.type ? b.type.toUpperCase() : '未知');
      } else {
        valA = a[sortColumn as keyof FileInfo];
        valB = b[sortColumn as keyof FileInfo];
      }


      let comparison = 0;
      if (sortColumn === 'size') {
        const sizeA = typeof valA === 'number' ? valA : -1; // Treat '-' or non-numeric as -1
        const sizeB = typeof valB === 'number' ? valB : -1;
        comparison = sizeA - sizeB;
      } else if (sortColumn === 'birthtime' || sortColumn === 'mtime') {
        const dateA = new Date(valA || 0).getTime(); // Handle potential null/undefined dates
        const dateB = new Date(valB || 0).getTime();
        comparison = dateA - dateB;
      } else { // name, type (string comparison)
         // Ensure values are strings for localeCompare
         const strA = (valA === null || valA === undefined) ? '' : String(valA);
         const strB = (valB === null || valB === undefined) ? '' : String(valB);
         comparison = strA.localeCompare(strB, undefined, { numeric: true, sensitivity: 'base' });
      }

      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [files, sortColumn, sortDirection]);


  return (
    <div className="fs-container">
      <div className="fs-header print:hidden">
        <div className="fs-header-top">
          <h1>文件系统</h1>
          {/* <Button
            icon={<SettingOutlined />}
            onClick={() => setIsEnvSettingVisible(true)}
          >
            环境设置
          </Button> */}
        </div>
        <div className="fs-breadcrumb">
          <div className="flex items-center space-x-2 flex-wrap">
            <a onClick={() => router.push(`/fs?type=${dirType}&path=/`)} className="text-blue-600 hover:underline cursor-pointer"><FaHome /></a>
            {currentPath.split('/').filter(Boolean).map((path, index, array) => (
              <React.Fragment key={path + index}>
                <span>/</span>
                <a onClick={() => {
                  const targetPath = '/' + array.slice(0, index + 1).join('/');
                  router.push(`/fs?type=${dirType}&path=${encodeURIComponent(targetPath)}`);
                 }} className="text-blue-600 hover:underline cursor-pointer">
                  {path}
                </a>
              </React.Fragment>
            ))}
          </div>
          <button
            onClick={navigateUp}
            disabled={currentPath === '/'}
            className={`ml-4 px-3 py-1 border rounded cursor-pointer ${currentPath === '/' ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-700 text-white'}`}
          >
            <FaArrowUp />
          </button>
        </div>
      </div>

      <div className="fs-content print:hidden">
        {isLoading ? (
          <div className="fs-loading">加载中...</div>
        ) : (
          <table className="fs-file-table">
            <thead>
              <tr>
                <th onClick={() => handleSort('name')} className="cursor-pointer hover:bg-gray-100 px-4 py-2">
                  <span className="flex items-center">
                    名称
                    {sortColumn === 'name' ? (sortDirection === 'asc' ? <FaSortUp className="ml-1" /> : <FaSortDown className="ml-1" />) : <FaSort className="ml-1 opacity-30" />}
                  </span>
                </th>
                <th onClick={() => handleSort('type')} className="cursor-pointer hover:bg-gray-100 px-4 py-2">
                   <span className="flex items-center">
                    类型
                    {sortColumn === 'type' ? (sortDirection === 'asc' ? <FaSortUp className="ml-1" /> : <FaSortDown className="ml-1" />) : <FaSort className="ml-1 opacity-30" />}
                  </span>
                </th>
                <th onClick={() => handleSort('size')} className="cursor-pointer hover:bg-gray-100 px-4 py-2">
                   <span className="flex items-center">
                    大小
                    {sortColumn === 'size' ? (sortDirection === 'asc' ? <FaSortUp className="ml-1" /> : <FaSortDown className="ml-1" />) : <FaSort className="ml-1 opacity-30" />}
                  </span>
                </th>
                <th onClick={() => handleSort('birthtime')} className="cursor-pointer hover:bg-gray-100 px-4 py-2">
                   <span className="flex items-center">
                    创建日期
                    {sortColumn === 'birthtime' ? (sortDirection === 'asc' ? <FaSortUp className="ml-1" /> : <FaSortDown className="ml-1" />) : <FaSort className="ml-1 opacity-30" />}
                  </span>
                </th>
                <th onClick={() => handleSort('mtime')} className="cursor-pointer hover:bg-gray-100 px-4 py-2">
                   <span className="flex items-center">
                    修改日期
                    {sortColumn === 'mtime' ? (sortDirection === 'asc' ? <FaSortUp className="ml-1" /> : <FaSortDown className="ml-1" />) : <FaSort className="ml-1 opacity-30" />}
                  </span>
                </th>
                <th className="px-4 py-2">操作</th>
              </tr>
            </thead>
            <tbody>
              {sortedFiles.map((file: FileInfo, index: number) => (
                <tr key={file.path || index}>
                  <td className="border-t px-4 py-2">
                    {file.is_dir ? (
                      <span className="directory flex items-center cursor-pointer hover:text-blue-600" onClick={() => navigateToDirectory(file.name)}>
                        <span className="mr-2">{getFileIcon('directory')}</span> {file.name}
                      </span>
                    ) : (
                      <span className="file flex items-center">
                         <button
                           className="text-blue-600 hover:underline cursor-pointer p-0 bg-transparent border-none flex items-center"
                           onClick={() => handlePreview(file)}
                         >
                           <span className="mr-2">{getFileIcon(file.type)}</span> {file.name}
                         </button>
                      </span>
                    )}
                  </td>
                  <td className="border-t px-4 py-2">{file.is_dir ? '文件夹' : (file.type ? file.type.toUpperCase() : '未知')}</td>
                  <td className="border-t px-4 py-2 text-right">{file.size ||  '-'}</td>
                  <td className="border-t px-4 py-2">{file.birthtime ? new Date(file.birthtime).toLocaleString() : '-'}</td>
                  <td className="border-t px-4 py-2">{file.mtime ? new Date(file.mtime).toLocaleString() : '-'}</td>
                  <td className="border-t px-4 py-2">
                    {file.is_dir ? (
                      <button
                        title="打开"
                        className="text-blue-600 hover:text-blue-800 cursor-pointer p-1 bg-transparent border-none mr-2"
                        onClick={() => navigateToDirectory(file.name)}
                      >
                        <FaFolder size={18}/>
                      </button>
                    ) : (
                      <React.Fragment>
                        <button
                          title="预览"
                          className="text-blue-600 hover:text-blue-800 cursor-pointer p-1 bg-transparent border-none mr-2"
                          onClick={() => handlePreview(file)}
                        >
                          <FaEye size={18}/>
                        </button>
                      </React.Fragment>
                    )}
                    <button
                      title="下载"
                      className="text-blue-600 hover:text-blue-800 cursor-pointer p-1 bg-transparent border-none mr-2"
                      onClick={() => handleDownload(file)}
                    >
                      <FaDownload size={18}/>
                    </button>
                    <button
                      title="删除"
                      className="text-red-600 hover:text-red-800 cursor-pointer p-1 bg-transparent border-none"
                      onClick={() => handleDelete(file)}
                    >
                      <FaTrash size={18}/>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {previewFile && (
        <FilePreviewModal
          onClose={() => {
            setPreviewFile(null);
            // 关闭预览时，移除URL中的preview参数
            router.push(`/fs?type=${dirType}&path=${encodeURIComponent(currentPath)}`);
          }}
          fileUrl={previewFile.url}
          fileName={previewFile.name}
          fileType={previewFile.type}
          dirType={dirType} // Pass dirType
          onDownload={() => {
            const file = files.find(f => f.name === previewFile.name);
            if (file) {
              handleDownload(file);
            }
          }}
        />
      )}

      <EnvSettingModal
        visible={isEnvSettingVisible}
        onClose={() => setIsEnvSettingVisible(false)}
      />

      <DeleteConfirmModal
        visible={!!deleteFile}
        file={deleteFile}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
      />
    </div>
  );
};

export default FileSystemPage;
