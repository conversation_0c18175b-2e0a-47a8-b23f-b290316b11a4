'use client';
import React from 'react';
import MarkdownPlus from '../MarkdownPlus';

const TableTestPage = () => {
  const markdownContent = `
# 表格字数限制功能测试

这个页面用于测试表格单元格的字数限制功能。

## 测试用户数据表格

下面的表格包含长文本内容，用于测试字数限制功能：

\`\`\`api-table:/api/test-data?type=users
{
  "pageSize": 5,
  "showSearch": true,
  "showPagination": true,
  "searchPlaceholder": "搜索用户..."
}
\`\`\`

## 功能说明

- **字数限制**: 超过50个字符的内容会显示为省略号
- **悬停提示**: 鼠标悬停在省略的内容上会显示完整文本
- **双击查看**: 双击省略的单元格会弹出模态框显示完整内容
- **样式提示**: 可以省略的单元格会有特殊的悬停样式

## 测试步骤

1. 查看"描述"列的内容是否正确显示省略号
2. 将鼠标悬停在省略的内容上，查看是否显示完整的tooltip
3. 双击省略的单元格，查看是否弹出完整内容的模态框
4. 测试搜索功能是否正常工作
5. 测试分页功能是否正常工作

## 其他测试数据

### 产品数据表格

\`\`\`api-table:/api/test-data?type=products
{
  "pageSize": 10,
  "showSearch": true,
  "showPagination": true
}
\`\`\`

### 大数据集测试

\`\`\`api-table:/api/test-data?type=large
{
  "pageSize": 20,
  "showSearch": true,
  "showPagination": true
}
\`\`\`
`;

  return (
    <div className="container mx-auto p-6">
      <MarkdownPlus content={markdownContent} />
    </div>
  );
};

export default TableTestPage;
