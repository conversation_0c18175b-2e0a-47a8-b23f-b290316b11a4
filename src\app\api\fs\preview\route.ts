import { NextRequest, NextResponse } from "next/server";
import { download, DIR } from "@/utils/fs";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const filePath = searchParams.get("file_path");
    const type = (searchParams.get("type") || "document") as DIR;

    if (!filePath) {
      return NextResponse.json({ error: "文件路径不能为空" }, { status: 400 });
    }

    const { fileBuffer, headers } = await download(type, filePath, true);

    return new NextResponse(fileBuffer, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error("文件预览失败:", error);
    return NextResponse.json(
      { error: (error as Error).message || '文件预览失败' },
      { status: 400 }
    );
  }
}
