'use client'
import React from 'react';

interface InfoCardItem {
  label: string;
  id: string;
  value: string | number | undefined;
  className?: string; // 添加可选的 className 属性
}

interface InfoCardProps {
  title: string;
  items: InfoCardItem[];
}

const InfoCard: React.FC<InfoCardProps> = ({ title, items }) => {
  return (
    <div className="info-card">
      <h3>{title}</h3>
      {items.map((item, index) => (
        <p key={index}>
          <strong>{item.label}:</strong>{' '}
          <span 
            id={item.id} 
            className={item.className}
          >
            {item.value}
          </span>
        </p>
      ))}
    </div>
  );
};

export default InfoCard;