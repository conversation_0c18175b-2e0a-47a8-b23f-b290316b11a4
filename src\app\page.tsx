import type { Metadata } from "next"; 
import Link from "next/link";

export const metadata: Metadata = {
  title: "PV 工具",
  description: "PV 工具",
};

 
function Home() { 
  return (
    <div className="min-h-screen bg-[var(--background-color)] flex items-center justify-center p-4">
      <div className="max-w-xl w-full bg-white rounded-xl shadow-[var(--card-shadow)] p-8 text-center">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] mb-6">欢迎使用文件识别工具</h1>
        <p className="text-[var(--text-color)] text-lg mb-8">请选择以下功能进行操作：</p>
        <div className="grid gap-4">
          {/* <Link href="/recognition"
                className="block p-4 rounded-lg border-2 border-[var(--primary-color)] text-[var(--primary-color)] transition-colors">
            识别工具
          </Link> */}
          <Link href="/fs"
                className="block p-4 rounded-lg border-2 border-[var(--primary-color)] text-[var(--primary-color)] transition-colors">
            文件查询
          </Link>
          <Link href="/fs?type=report"
                className="block p-4 rounded-lg border-2 border-[var(--primary-color)] text-[var(--primary-color)] transition-colors">
            报告数据查询
          </Link>
          <Link href="/chat"
                className="block p-4 rounded-lg border-2 border-[var(--primary-color)] text-[var(--primary-color)] transition-colors">
            ChatView
          </Link>
           
        </div>
      </div>
    </div>
  );
}

export default Home;
