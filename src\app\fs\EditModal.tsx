import React from 'react';

interface EditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  value: string;
  onChange: (value: string) => void;
  title: string;
}

export const EditModal: React.FC<EditModalProps> = ({ isOpen, onClose, onSave, value, onChange, title }) => {
  if (!isOpen) return null;
  
  // 判断内容是否过长或包含换行符，如果是则使用 textarea
  const isLongContent = value.length > 80 || value.includes('\n');
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // 如果是 textarea 且按下 Enter 键，不自动保存，允许换行
    if (e.key === 'Enter' && !isLongContent) {
      e.preventDefault();
      onSave();
    } else if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'Enter' && e.ctrlKey) {
      // Ctrl+Enter 保存，适用于 textarea
      e.preventDefault();
      onSave();
    }
  };
  
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>
      <div className="bg-white rounded-lg shadow-xl p-5 max-w-3xl w-full relative z-10">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            &times;
          </button>
        </div>
        <div className="mb-4">
          {isLongContent ? (
            <textarea
              value={value}
              onChange={(e) => onChange(e.target.value)}
              onKeyDown={handleKeyDown}
              className="w-full px-3 py-2 border border-blue-300 h-[60vh] rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              rows={5}
              autoFocus
              placeholder="按 Ctrl+Enter 保存"
            />
          ) : (
            <input
              type="text"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              onKeyDown={handleKeyDown}
              className="w-full px-3 py-2 border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              autoFocus
            />
          )}
          {isLongContent && (
            <div className="text-xs text-gray-500 mt-1">提示: 按 Ctrl+Enter 保存</div>
          )}
        </div>
        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded hover:bg-gray-100"
          >
            取消
          </button>
          <button
            onClick={onSave}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            保存
          </button>
        </div>
      </div>
    </div>
  );
};
