version: '3.8'

services:
  uat:
    build:
      context: .
      network: host
    container_name: pv-manus-front-uat
    # build:
    #   context: .
    #   target: runner
    ports:
      - "3202:3000"
    user: root
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    restart: always
    volumes:
      - /home/<USER>/uat/volumes/documents:/app/documents
      - /home/<USER>/uat/volumes/reports:/app/reports
      - /usr/share/nginx/html/uat/argus_robot:/app/public
    # deploy:
    #   resources:
    #     limits:
    #       cpus: '1'
    #       memory: 1G
    #     reservations:
    #       cpus: '0.5'
    #       memory: 512M