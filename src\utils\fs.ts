import fs from "fs/promises";
import path from "path";
import dayjs from "dayjs";
import mime from "mime-types";
import JSZip from 'jszip';

// import { gitClient } from "./git";

// gitClient.init();

export interface FileInfo {
  name: string;
  is_dir: boolean;
  path: string;
  type?: string;
  size?: string;
  birthtime?: string;
  mtime?: string;
}

export interface DirectoryResponse {
  current_path: string;
  parent_path: string | null;
  items: FileInfo[];
  error: string | null;
}

export interface PreviewFileInfo {
  url: string;
  name: string;
  type: string;
}

export const REPORT_BASE_DIR = process.env.REPORTBUG_BASE_DIR || "./reports";
export const DOCUMENTS_DIR = process.env.DOCUMENTS_BASE_DIR || "./documents";

export type DIR = "report" | "document";
export const getBaseDir = (type: DIR) => {
  if (type === "report") {
    return REPORT_BASE_DIR;
  }
  if (type === "document") {
    return DOCUMENTS_DIR;
  }
  return "";
};

// 获取文件类型
export function getFileType(fileName: string): string {
  const ext = path.extname(fileName).toLowerCase();
  return ext ? ext.slice(1) : "";
}

// 获取文件大小的格式化函数
export function formatFileSize(bytes: number): string {
  const units = ["B", "KB", "MB", "GB", "TB"];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

export const list = async (type: DIR, subdir: string = "", search?: string) => {
  const baseDir = getBaseDir(type);
  const fullPath = path.resolve(baseDir, subdir);
  if (!fullPath.startsWith(path.resolve(baseDir))) {
    throw new Error("访问路径不允许");
  }

  // 检查目录是否存在
  try {
    await fs.access(fullPath);
  } catch (error) {
    throw new Error("访问路径不允许");
  }

  // 读取目录内容
  const items = await fs.readdir(fullPath, { withFileTypes: true });

  // 转换为 FileInfo[] 格式
  let fileInfos: FileInfo[] = await Promise.all(
    items.map(async (item) => {
      const itemPath = path.join(subdir, item.name);
      const fullItemPath = path.join(fullPath, item.name);
      const fileInfo: FileInfo = {
        name: item.name,
        is_dir: item.isDirectory(),
        path: itemPath.replace(/\\/g, "/"),
      };
      const stats = await fs.stat(fullItemPath);
      fileInfo.birthtime = dayjs(stats.birthtime).format("YYYY-MM-DD HH:mm:ss");
      fileInfo.mtime = dayjs(stats.mtime).format("YYYY-MM-DD HH:mm:ss");
      if (!item.isDirectory()) {
        fileInfo.size = formatFileSize(stats.size);
        fileInfo.type = getFileType(item.name);
      }

      return fileInfo;
    })
  );

  // 如果有搜索关键词，进行过滤
  if (search) {
    const searchLower = search.toLowerCase();
    fileInfos = fileInfos.filter((item) =>
      item.name.toLowerCase().includes(searchLower)
    );
  }

  // 构建响应
  const response: DirectoryResponse = {
    current_path: subdir,
    parent_path: subdir ? path.dirname(subdir) : null,
    items: fileInfos,
    error: null,
  };

  return response;
};

export const upload = async (
  type: DIR,
  filePath: string,
  content: string
) => {
  if (!filePath) {
    throw new Error("文件路径不能为空");
  }
  const baseDir = getBaseDir(type);

  // 构建完整路径，并进行安全检查
  const fullPath = path.resolve(baseDir, filePath);
  if (!fullPath.startsWith(path.resolve(baseDir))) {
    throw new Error("访问路径不允许");
  }

  // 检查目录是否存在，如果不存在则创建
  const dirPath = path.dirname(fullPath);
  try {
    await fs.mkdir(dirPath, { recursive: true });
  } catch (error) {
    throw new Error("创建目录失败");
  }

  // 写入文件
  try {
    await fs.writeFile(fullPath, content, "utf-8");
    return { message: "上传成功" };
  } catch (error) {
    console.error('error :>> ', error);
    throw new Error("写入文件失败");
  }
};

export const download = async (
  type: DIR,
  filePath: string,
  preview = false
) => {
  if (!filePath) {
    throw new Error("文件路径不能为空");
  }
  const baseDir = getBaseDir(type);

  // 构建完整路径，并进行安全检查
  const fullPath = path.resolve(baseDir, filePath);
  
  if (!fullPath.startsWith(path.resolve(baseDir))) {
    throw new Error("访问路径不允许");
  }

  // 检查文件是否存在
  try {
    await fs.access(fullPath);
  } catch (error) {
    throw new Error("文件不存在");
  }

  // 获取文件状态
  const stats = await fs.stat(fullPath);
  if (stats.isDirectory()) {
    throw new Error("不能预览目录");
  }

  // 获取文件扩展名并确定 MIME 类型
  const ext = path.extname(filePath).toLowerCase().slice(1);
  const contentType = mime.contentType(ext) || "application/octet-stream";
  // 读取文件并创建流响应
  const fileBuffer = await fs.readFile(fullPath);

  // 设置响应头
  const headers = new Headers();
  headers.set("Content-Type", contentType);
  headers.set("Content-Length", stats.size.toString());
  headers.set(
    "Content-Disposition",
    `${preview ? "inline" : "attachment"}; filename="${encodeURIComponent(
      path.basename(filePath)
    )}"`
  );
  return { fileBuffer, headers };
};

/**
 * 将文件压缩成zip
 * @param fullPath 文件路径
 * @returns zip buffer 压缩后的buffer
 */
export const compressToZip = async (fullPath: string): Promise<Buffer> => {
  const zip = new JSZip();
  
  // 检查是否为目录
  const stats = await fs.stat(fullPath);
  if (stats.isDirectory()) {
    // 递归读取目录内容
    const addFilesToZip = async (currentPath: string, relativePath: string = '') => {
      const items = await fs.readdir(currentPath, { withFileTypes: true });
      
      for (const item of items) {
        const itemPath = path.join(currentPath, item.name);
        const itemRelativePath = path.join(relativePath, item.name);
        
        if (item.isDirectory()) {
          // 递归处理子目录
          await addFilesToZip(itemPath, itemRelativePath);
        } else {
          // 添加文件到zip,使用相对路径保持目录结构
          const content = await fs.readFile(itemPath);
          zip.file(itemRelativePath.replace(/\\/g, '/'), content);
        }
      }
    };
    
    await addFilesToZip(fullPath);
  } else {
    // 单文件处理
    const content = await fs.readFile(fullPath);
    const fileName = path.basename(fullPath);
    zip.file(fileName, content);
  }
  
  return await zip.generateAsync({type: "nodebuffer"});
};

export const dirDownload = async (
  type: DIR,
  dirPath: string,
) => {
  if (!dirPath) {
    throw new Error("目录路径不能为空");
  }
  const baseDir = getBaseDir(type);

  // 构建完整路径，并进行安全检查
  const fullPath = path.resolve(baseDir, dirPath);
  if (!fullPath.startsWith(path.resolve(baseDir))) {
    throw new Error("访问路径不允许");
  }

  // 检查目录是否存在
  try {
    await fs.access(fullPath);
  } catch (error) {
    throw new Error("目录不存在");
  }
  
  const zipBuffer = await compressToZip(fullPath);
  
  // 设置响应头
  const headers = new Headers();
  headers.set("Content-Type", "application/zip");
  headers.set("Content-Length", zipBuffer.length.toString());
  headers.set(
    "Content-Disposition",
    `attachment; filename="${encodeURIComponent(path.basename(dirPath))}.zip"`
  );
  
  return { fileBuffer: zipBuffer, headers };
};

export const deleteFile = async (
  type: DIR,
  filePath: string
) => {
  if (!filePath) {
    throw new Error("文件路径不能为空");
  }
  const baseDir = getBaseDir(type);

  // 构建完整路径，并进行安全检查
  const fullPath = path.resolve(baseDir, filePath);
  if (!fullPath.startsWith(path.resolve(baseDir))) {
    throw new Error("访问路径不允许");
  }

  // 检查文件或目录是否存在
  try {
    await fs.access(fullPath);
  } catch (error) {
    throw new Error("文件或目录不存在");
  }

  // 获取文件状态
  const stats = await fs.stat(fullPath);

  try {
    if (stats.isDirectory()) {
      // 递归删除目录
      await fs.rm(fullPath, { recursive: true, force: true });
      return { message: "目录删除成功" };
    } else {
      // 删除文件
      await fs.unlink(fullPath);
      return { message: "文件删除成功" };
    }
  } catch (error) {
    console.error('删除失败:', error);
    throw new Error("删除失败");
  }
};