import { NextRequest, NextResponse } from 'next/server';  
import { list, DIR } from "@/utils/fs";
  

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const subdir = searchParams.get('subdir') || '';
    const search = searchParams.get('search') as string | undefined;
    const type = (searchParams.get("type") || "document") as DIR;

    const response = await list(type, subdir, search); 
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('目录列表获取失败:', error);
    return NextResponse.json(
      { error: (error as Error).message || '目录列表获取失败' },
      { status: 400 }
    );
  }
}