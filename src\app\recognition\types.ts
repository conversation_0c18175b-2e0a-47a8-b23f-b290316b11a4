/**
 * 全局类型定义文件
 */

// 用户信息类型
export interface UserInfo {
  userId: string;
  userName: string;
  userRole?: string;
}

// 租户信息类型
export interface TenantInfo {
  tenantId: string;
  tenantName: string;
  tenantEnv: string;
  studyNum: string;
}

// 租户条目类型
export interface TenantEntry {
  tenantId: string;
  tenantName: string;
}

// 应用设置类型
export interface AppSettings {
  currentLanguage: 'en' | 'cn';
  environment: 'dev' | 'test' | 'uat';
  operationMode: 'auto' | 'manual';
  devEnv: string;
  testEnv: string;
  uatEnv: string;
  renderFormat: 'markdown' | 'mindmap' | 'tree';
  reportType: 'initial' | 'follow-up';
}

// 文件上传响应类型
export interface FileUploadResponse {
  success: boolean;
  md5?: string;
  images?: string[];
  fileName?: string;
  error?: string;
}

// 识别结果类型
export interface RecognitionResult {
  content: string;
  labels?: any;
}

// 字段数据类型
export interface FieldData {
  key: string;
  value: any;
  type: 'required' | 'optional' | 'readonly';
  path: string;
}

// 模块占位符项类型
export interface ModulePlaceholderItem {
  module: string;
  placeholder: string | string[];
}

// 事件描述数据类型
export interface EventDescriptionData {
  description: string;
}

// 状态数据格式
export interface AppState {
  basePath: string;
  settings: AppSettings;
  lastSwitchTime: number;
  switchInterval: number;
  eventDescription: string;
  recognitionResults: RecognitionResult[];
  isRecognitionModified: boolean;
  isStructuredDataModified: boolean;
  isTransformedDataModified: boolean;
  fileName: string;
  insightData: any;
  finalStructuredData: any;
  finalTransformedData: any;
  pdfMd5: string;
  pdfTotalPage: number;
  startTime: Date | string | null;
  endTime: Date | string | null;
  isLoading: boolean;
  hasFinishedLoading: boolean;
  promptsData: any;
  modifiedPrompts: Set<string>;
  lastActiveEditor: string | null;
  lastActiveTime: number;
  isDataReviewModified: boolean;
  fieldMapping: Record<string, string>;
  reverseFieldMapping: Record<string, string>;
  dataReviewData: any;
  userId: string;
  userName: string;
  tenantId: string;
  tenantName: string;
  tenantEnv: string;
  studyNum: string;
  tenantList: TenantEntry[];
  modulesPlaceholders: ModulePlaceholderItem[];
  i18n: Record<string, Record<string, string>>;
  fieldData: FieldData[];
  hasError: boolean;
  drugDictionaryData: {
    initialContent: string;
    currentContent: string;
  };
  currentNode: string;
  progress: number;
}