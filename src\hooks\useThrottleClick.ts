import { useCallback, useRef, MouseEventHandler } from 'react';

/**
 * 使用 useRef 优化的防重复点击 Hook
 * @param {Function} callback 点击时要执行的回调函数
 * @param {number} delay 防重复点击的延迟时间（毫秒）
 * @returns {Function} 包装后的回调函数
 */
function useThrottleClick(callback: ( ) => void, delay = 2000) {
  const isThrottled = useRef(false);
  
  const throttledCallback = useCallback(( ) => {
    if (!isThrottled.current) {
      isThrottled.current = true;
      callback( );
      
      setTimeout(() => {
        isThrottled.current = false;
      }, delay);
    }
  }, [callback, delay]);
  
  return throttledCallback;
}

export default useThrottleClick;
