"use client";
import React, { useState, useCallback, useMemo, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation"; 
import ChatInterface from "@/components/chat-view/index"; 

const Page: React.FC = () => {
  // const router = useRouter();
  // const params = useSearchParams();
  // const md5 = params.get('md5') 

  return (
    <div className="w-full h-screen bg-gray-100 dark:bg-gray-900"> 
      <div className="w-full h-full border-r border-gray-200 dark:border-gray-700">
        <ChatInterface loading={false}   />
      </div>
      
      
    </div>
  );
};

export default Page;

