# 租户初始化页面
 

### 租户列表

```api-table:/kb-server/tenant/tenantList
{
  "buttons": [
    {
      "id": "edit-tenant",
      "text": "编辑租户",
      "description": "编辑租户信息",
      "type": "row",
      "variant": "primary",
      "method": "POST",
      "url": "/kb-server/tenant/tenantUpdate",
      "rowDataMapping": {
        "id": "id",
        "tenantId": "tenantId",
        "tenantName": "tenantName"
      },
      "params": [
        {
          "name": "id",
          "type": "string",
          "required": true,
          "description": "ID"
        },
        {
          "name": "tenantId",
          "type": "string",
          "required": true,
          "description": "tenantId"
        },
        {
          "name": "tenantName",
          "type": "string",
          "required": true,
          "description": "tenantName"
        }
      ]
    }
  ]
}
```
### 租户绑定列表

```api-table:/kb-server/tenant/tenantRelList
{
  "buttons": [
    {
      "id": "edit-tenant",
      "text": "注册租户信息",
      "description": "注册租户信息",
      "type": "row",
      "variant": "primary",
      "method": "POST",
      "url": "/kb-server/tenant/tenantAppRegister",
      "rowDataMapping": {
        "tenantAppRelId": "tenantAppRelId",
        "tenantId": "tenantId",
        "tenantName": "tenantName"
      },
      "params": [
        {
          "name": "tenantAppRelId",
          "type": "string",
          "required": true,
          "description": "ID"
        },
        {
          "name": "tenantId",
          "type": "string",
          "required": true,
          "description": "tenantId"
        },
        {
          "name": "tenantName",
          "type": "string",
          "required": true,
          "description": "tenantName"
        },
        {
          "name": "sourceTenantId",
          "type": "string",
          "required": false,
          "defaultValue": "LaNova",
          "description": "sourceTenantId"
        },
        {
          "name": "sourceCompanyName",
          "type": "string",
          "required": false,
          "defaultValue": "礼新",
          "description": "sourceCompanyName"
        },{
          "name": "language",
          "type": "enum",
          "enumOptions": ["cn", "en"],
          "required": false,
          "defaultValue": "cn",
          "description": "language"
        }
      ]
    }
  ]
}
```


#### 从Excel导入字典标准数据

```api-caller:{
  "id": "dict-importExcel",
  "name": "从Excel导入字典标准数据",
  "description": "从Excel导入字典标准数据",
  "method": "POST",
  "url": "/kb-server/dict/standard/importExcel",
  "params": [
    {
      "name": "file",
      "type": "file",
      "required": true
    }
  ]
}
```



### 分页查询试验方案信息

```api-table:/kb-server/api/pms/study/page
```
### 分页查询字典映射信息

```api-table:/kb-server/api/pms/dictionary/page
``` 

### PMC分页查询表数据

```api-table:/kb-server/api/pmc/query
{
  "pageSize": 10,
  "showSearch": true,
  "showPagination": true, 
  "queryParams": [
    {
      "name": "tableName",
      "label": "tableName",
      "type": "dict",
      "dictUrl": "/kb-server/api/pmc/tables"
    }
  ]
}
```

### 分页查询字典标准数据

```api-table:/kb-server/api/kb/pageDictStandard
```


### 分页查询转换配置数据

```api-table:/kb-server/api/kb/transform/page
```


### 分页查询租户Meddra版本数据

```api-table:/kb-server/api/kb/meddra/page
```



### 分页查询租户Whodrug版本数据

```api-table:/kb-server/api/kb/whodrug/page
```
