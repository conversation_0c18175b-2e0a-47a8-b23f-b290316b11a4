import React, { useState, useRef } from 'react';
import dynamic from 'next/dynamic';
import { BiErrorCircle } from 'react-icons/bi';

const JsonView = dynamic(() => import('@microlink/react-json-view'), {
  ssr: false,
});

// 标准 API 响应结构
interface StandardApiResponse {
  data: any;
  code: number; // 10000 为成功
  message: string; // 错误信息
}

// API调用历史记录类型
interface ApiCallRecord {
  id: string;
  timestamp: string;
  method: string;
  url: string;
  headers: Record<string, string>;
  params: Record<string, any>;
  response: any;
  status: number;
  duration: number;
  success: boolean;
  buttonText?: string; // 添加按钮文本字段，用于表格按钮调用
}

// API参数定义类型
interface ApiParam {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'json' | 'enum' | 'file' | 'files' | 'dict';
  required: boolean;
  defaultValue?: any;
  description?: string;
  enumOptions?: string[]; // 枚举选项
  accept?: string; // 文件类型限制，如 "image/*,application/pdf"
  dictUrl?: string; // dict 类型的数据源 URL
  dictOptions?: string[]; // dict 类型加载的选项数据
}

// API接口定义类型
interface ApiDefinition {
  id: string;
  name: string;
  description: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  url: string;
  headers?: Record<string, string>;
  params: ApiParam[];
}

// API调用组件
interface ApiCallerProps {
  definition: ApiDefinition;
  className?: string;
  onSuccess?: () => void; // 成功回调函数
}

// 参数输入组件
const ApiParamInput: React.FC<{
  param: ApiParam;
  value: any;
  files: Record<string, FileList | File[]>;
  dictData: Record<string, string[]>;
  dictLoading: Record<string, boolean>;
  onParamChange: (paramName: string, value: any) => void;
  onFileChange: (paramName: string, fileList: FileList | null) => void;
}> = ({ param, value, files, dictData, dictLoading, onParamChange, onFileChange }) => {
  switch (param.type) {
    case 'boolean':
      return (
        <select
          value={value?.toString() || ''}
          onChange={(e) => onParamChange(param.name, e.target.value === 'true')}
          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">请选择</option>
          <option value="true">true</option>
          <option value="false">false</option>
        </select>
      );
    
    case 'number':
      return (
        <input
          type="number"
          value={value || ''}
          onChange={(e) => onParamChange(param.name, parseFloat(e.target.value) || '')}
          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder={param.description || `请输入${param.name}`}
        />
      );
    
    case 'enum':
      return (
        <select
          value={value || ''}
          onChange={(e) => onParamChange(param.name, e.target.value)}
          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">请选择</option>
          {param.enumOptions?.map((option, index) => (
            <option key={index} value={option}>
              {option}
            </option>
          ))}
        </select>
      );
    
    case 'file':
      return (
        <div>
          <input
            type="file"
            accept={param.accept}
            onChange={(e) => onFileChange(param.name, e.target.files)}
            className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          {files[param.name] && files[param.name].length > 0 && (
            <div className="mt-2 text-sm text-gray-600">
              已选择: {files[param.name][0].name} ({(files[param.name][0].size / 1024).toFixed(2)} KB)
            </div>
          )}
        </div>
      );
    
    case 'files':
      return (
        <div>
          <input
            type="file"
            multiple
            accept={param.accept}
            onChange={(e) => onFileChange(param.name, e.target.files)}
            className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          {files[param.name] && files[param.name].length > 0 && (
            <div className="mt-2 text-sm text-gray-600">
              已选择 {files[param.name].length} 个文件:
              <ul className="list-disc list-inside mt-1">
                {Array.from(files[param.name]).map((file, index) => (
                  <li key={index}>
                    {file.name} ({(file.size / 1024).toFixed(2)} KB)
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      );
    
    case 'json':
      return (
        <textarea
          value={typeof value === 'string' ? value : JSON.stringify(value, null, 2)}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              onParamChange(param.name, parsed);
            } catch {
              onParamChange(param.name, e.target.value);
            }
          }}
          rows={4}
          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
          placeholder='{"key": "value"}'
        />
      );

    case 'dict':
      const options = dictData[param.name] || [];
      const isLoading = dictLoading[param.name] || false;

      return (
        <div>
          <select
            value={value || ''}
            onChange={(e) => onParamChange(param.name, e.target.value)}
            disabled={isLoading}
            className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
          >
            <option value="">请选择</option>
            {options.map((option, index) => (
              <option key={index} value={option}>
                {option}
              </option>
            ))}
          </select>
          {isLoading && (
            <div className="mt-1 text-sm text-gray-500">加载选项中...</div>
          )}
          {!isLoading && options.length === 0 && (
            <div className="mt-1 text-sm text-red-500">无可用选项</div>
          )}
        </div>
      );

    default:
      return (
        <input
          type="text"
          value={value || ''}
          onChange={(e) => onParamChange(param.name, e.target.value)}
          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder={param.description || `请输入${param.name}`}
        />
      );
  }
};

const ApiCaller: React.FC<ApiCallerProps> = ({ definition, className = '', onSuccess }) => {
  const [params, setParams] = useState<Record<string, any>>(() => {
    const initialParams: Record<string, any> = {};
    definition.params.forEach(param => {
      if (param.defaultValue !== undefined) {
        initialParams[param.name] = param.defaultValue;
      }
    });
    return initialParams;
  });

  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<any>(null);
  const [responseType, setResponseType] = useState<'json' | 'text' | 'other'>('json');
  const [error, setError] = useState<string>('');
  const [callHistory, setCallHistory] = useState<ApiCallRecord[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [dictData, setDictData] = useState<Record<string, string[]>>({});
  const [dictLoading, setDictLoading] = useState<Record<string, boolean>>({});
  const [expandedRecord, setExpandedRecord] = useState<string | null>(null);
  const [files, setFiles] = useState<Record<string, FileList | File[]>>({});
  const startTimeRef = useRef<number>(0);

  // 加载 dict 类型参数的数据
  const loadDictData = React.useCallback(async (param: ApiParam) => {
    if (param.type !== 'dict' || !param.dictUrl) return;

    setDictLoading(prev => ({ ...prev, [param.name]: true }));

    try {
      const response = await fetch(param.dictUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // 检查是否为标准 API 响应结构
      let data: string[] = [];
      if (result.code === 10000 && Array.isArray(result.data)) {
        data = result.data;
      } else if (Array.isArray(result)) {
        data = result;
      } else {
        throw new Error('Invalid response format');
      }

      setDictData(prev => ({ ...prev, [param.name]: data }));

      // 如果参数没有默认值且数据不为空，设置第一个选项为默认值
      if (!params[param.name] && data.length > 0) {
        handleParamChange(param.name, data[0]);
      }
    } catch (error) {
      console.error(`Failed to load dict data for ${param.name}:`, error);
      setDictData(prev => ({ ...prev, [param.name]: [] }));
    } finally {
      setDictLoading(prev => ({ ...prev, [param.name]: false }));
    }
  }, [params]);

  // 初始化加载所有 dict 类型参数的数据
  React.useEffect(() => {
    definition.params.forEach(param => {
      if (param.type === 'dict') {
        loadDictData(param);
      }
    });
  }, [definition.params, loadDictData]);

  // 处理参数值变化
  const handleParamChange = (paramName: string, value: any) => {
    setParams(prev => ({
      ...prev,
      [paramName]: value
    }));
  };

  // 处理文件变化
  const handleFileChange = (paramName: string, fileList: FileList | null) => {
    if (fileList) {
      setFiles(prev => ({
        ...prev,
        [paramName]: fileList
      }));
    } else {
      setFiles(prev => {
        const newFiles = { ...prev };
        delete newFiles[paramName];
        return newFiles;
      });
    }
  };

  // 验证参数
  const validateParams = (): string | null => {
    for (const param of definition.params) {
      if (param.required) {
        if (param.type === 'file' || param.type === 'files') {
          const fileList = files[param.name];
          if (!fileList || fileList.length === 0) {
            return `文件 "${param.name}" 是必填项`;
          }
        } else if (params[param.name] === undefined || params[param.name] === '') {
          return `参数 "${param.name}" 是必填项`;
        }
      }
    }
    return null;
  };

  // 调用API
  const callApi = async () => {
    const validationError = validateParams();
    if (validationError) {
      setError(validationError);
      return;
    }

    setLoading(true);
    setError('');
    setResponse(null);
    setResponseType('json');
    startTimeRef.current = Date.now();

    try {
      let url = definition.url;
      let body: any = undefined;
      const headers: Record<string, string> = { ...definition.headers };

      // 检查是否有文件参数
      const hasFiles = definition.params.some(param =>
        (param.type === 'file' || param.type === 'files') && files[param.name]
      );

      // 处理参数
      if (definition.method === 'GET') {
        // GET请求：参数放在URL中（文件不支持GET）
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            searchParams.append(key, String(value));
          }
        });
        const queryString = searchParams.toString();
        if (queryString) {
          url += (url.includes('?') ? '&' : '?') + queryString;
        }
      } else {
        if (hasFiles) {
          // 有文件上传时使用 FormData
          const formData = new FormData();
          
          // 添加普通参数
          Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== '') {
              formData.append(key, String(value));
            }
          });
          
          // 添加文件
          Object.entries(files).forEach(([key, fileList]) => {
            const param = definition.params.find(p => p.name === key);
            if (param?.type === 'files') {
              // 多文件上传
              Array.from(fileList).forEach(file => {
                formData.append(key, file);
              });
            } else if (param?.type === 'file' && fileList.length > 0) {
              // 单文件上传
              formData.append(key, fileList[0]);
            }
          });
          
          body = formData;
          // 不设置 Content-Type，让浏览器自动设置 multipart/form-data
        } else {
          // 无文件时使用 JSON
          headers['Content-Type'] = 'application/json';
          body = JSON.stringify(params);
        }
      }

      const fetchOptions: RequestInit = {
        method: definition.method,
        headers,
      };

      if (body) {
        fetchOptions.body = body;
      }

      const response = await fetch(url, fetchOptions);
      const duration = Date.now() - startTimeRef.current;
      
      let responseData;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
        setResponseType('json');
      } else {
        responseData = await response.text();
        setResponseType('text');
      }

      // 检查是否为标准 API 响应结构
      let actualData = responseData;
      let isSuccess = response.ok;
      let errorMessage = '';

      if (typeof responseData === 'object' && responseData !== null && 'code' in responseData) {
        // 标准 API 响应结构
        const standardResponse = responseData as StandardApiResponse;
        isSuccess = standardResponse.code === 10000;
        actualData = isSuccess ? standardResponse.data : responseData;
        if (!isSuccess) {
          errorMessage = standardResponse.message || '请求失败';
        }
      } else if (!response.ok) {
        // 非标准结构，使用 HTTP 状态判断
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      }

      // 创建调用记录
      const record: ApiCallRecord = {
        id: `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        timestamp: new Date().toISOString(),
        method: definition.method,
        url,
        headers,
        params,
        response: responseData, // 保存原始响应用于历史记录
        status: response.status,
        duration,
        success: isSuccess
      };

      setCallHistory(prev => [record, ...prev]);
      setResponse(actualData); // 显示实际数据
      
      if (!isSuccess) {
        setError(errorMessage);
      } else {
        // API调用成功，触发成功回调
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (err) {
      const duration = Date.now() - startTimeRef.current;
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      
      // 创建错误记录
      const record: ApiCallRecord = {
        id: `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        timestamp: new Date().toISOString(),
        method: definition.method,
        url: definition.url,
        headers: definition.headers || {},
        params,
        response: { error: errorMessage },
        status: 0,
        duration,
        success: false
      };

      setCallHistory(prev => [record, ...prev]);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 导出调用历史
  const exportHistory = () => {
    const dataStr = JSON.stringify(callHistory, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `api_call_history_${definition.id}_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 清空历史记录
  const clearHistory = () => {
    setCallHistory([]);
  };

  return (
    <div className={`api-caller bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      {/* 接口信息 */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{definition.name}</h3>
            <p className="text-sm text-gray-600 mt-1">{definition.description}</p>
            <div className="flex items-center mt-2 space-x-2">
              <span className={`px-2 py-1 text-xs font-medium rounded ${
                definition.method === 'GET' ? 'bg-green-100 text-green-800' :
                definition.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                definition.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {definition.method}
              </span>
              <code className="text-sm text-gray-700 bg-gray-100 px-2 py-1 rounded">{definition.url}</code>
            </div>
          </div>
          <button
            onClick={() => setShowHistory(!showHistory)}
            className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 rounded transition-colors"
          >
            {showHistory ? '隐藏历史' : `历史记录 (${callHistory.length})`}
          </button>
        </div>
      </div>

      {/* 参数输入 */}
      {definition.params.length > 0 && (
        <div className="p-4 border-b border-gray-200">
          <h4 className="text-md font-medium text-gray-900 mb-3">参数设置</h4>
          <div className="space-y-4">
            {definition.params.map((param) => (
              <div key={param.name} className="grid grid-cols-1 md:grid-cols-4 gap-2 items-start">
                <div className="md:col-span-1">
                  <label className="block text-sm font-medium text-gray-700">
                    {param.name}
                    {param.required && <span className="text-red-500 ml-1">*</span>}
                  </label>
                  <span className="text-xs text-gray-500">{param.type}</span>
                </div>
                <div className="md:col-span-2">
                  <ApiParamInput
                    param={param}
                    value={params[param.name]}
                    files={files}
                    dictData={dictData}
                    dictLoading={dictLoading}
                    onParamChange={handleParamChange}
                    onFileChange={handleFileChange}
                  />
                </div>
                <div className="md:col-span-1">
                  {param.description && (
                    <p className="text-xs text-gray-500 mt-1">{param.description}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 调用按钮 */}
      <div className="p-4 border-b border-gray-200">
        <button
          onClick={callApi}
          disabled={loading}
          className={`w-full py-2 px-4 rounded font-medium transition-colors ${
            loading
              ? 'bg-gray-400 text-white cursor-not-allowed'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
        >
          {loading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              调用中...
            </div>
          ) : (
            `${definition.name}`
          )}
        </button>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="p-4 border-b border-gray-200">
          <div className="bg-red-50 border border-red-200 rounded p-3">
            <div className="flex items-center">
              <BiErrorCircle className="text-red-500 text-lg mr-2" />
              <div>
                <p className="text-red-800 font-medium">调用失败</p>
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 响应结果 */}
      {response && (
        <div className="p-4 border-b border-gray-200">
          <h4 className="text-md font-medium text-gray-900 mb-2">响应结果</h4>
          <div className="bg-gray-50 rounded p-3 max-h-96 overflow-auto">
            {(() => {
              // 根据响应类型和数据类型选择合适的显示方式
              if (responseType === 'text' || typeof response === 'string') {
                // 文本响应或字符串类型，直接显示
                return (
                  <div>
                    <div className="mb-2 text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded inline-block">
                      文本响应
                    </div>
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {response}
                    </pre>
                  </div>
                );
              } else if (typeof response === 'number' || typeof response === 'boolean') {
                // 数字或布尔值，转换为字符串显示
                return (
                  <div>
                    <div className="mb-2 text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded inline-block">
                      {typeof response} 类型
                    </div>
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {String(response)}
                    </pre>
                  </div>
                );
              } else if (response === null) {
                // null 值
                return (
                  <div>
                    <div className="mb-2 text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded inline-block">
                      null 值
                    </div>
                    <pre className="text-sm text-gray-500 whitespace-pre-wrap">
                      null
                    </pre>
                  </div>
                );
              } else if (typeof response === 'object' && responseType === 'json') {
                // JSON 响应，尝试使用 JsonView
                try {
                  return (
                    <div>
                      <div className="mb-2 text-xs bg-green-100 text-green-700 px-2 py-1 rounded inline-block">
                        JSON 响应
                      </div>
                      <JsonView
                        src={response}
                        theme={{
                          base00: '#f8fafc', // 背景色 - 浅灰
                          base01: '#f1f5f9', // 折叠/展开按钮背景
                          base02: '#e2e8f0', // 边框色
                          base03: '#94a3b8', // 注释/标点符号
                          base04: '#64748b', // 普通文本
                          base05: '#475569', // 键名
                          base06: '#334155', // 高亮键名
                          base07: '#1e293b', // 最亮的文本
                          base08: '#dc2626', // 错误信息
                          base09: '#ea580c', // 数字
                          base0A: '#d97706', // 布尔值
                          base0B: '#16a34a', // 字符串
                          base0C: '#0891b2', // 日期
                          base0D: '#2563eb', // URL
                          base0E: '#7c3aed', // 正则表达式
                          base0F: '#c2410c'  // 其他值
                        }}
                        name={null}
                        collapsed={2}
                        displayObjectSize={true}
                        displayDataTypes={false}
                        enableClipboard={true}
                        quotesOnKeys={false}
                        sortKeys={true}
                        style={{
                          padding: '1rem',
                          borderRadius: '0.375rem',
                          fontFamily: 'monospace',
                          backgroundColor: '#f8fafc'
                        }}
                      />
                    </div>
                  );
                } catch (error) {
                  // JsonView 渲染失败，回退到 JSON.stringify
                  return (
                    <div>
                      <div className="mb-2 text-xs text-red-500 bg-red-100 px-2 py-1 rounded inline-block">
                        JSON 解析失败，显示原始数据
                      </div>
                      <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                        {JSON.stringify(response, null, 2)}
                      </pre>
                    </div>
                  );
                }
              } else if (typeof response === 'object') {
                // 对象类型但不是 JSON 响应，使用 JSON.stringify
                return (
                  <div>
                    <div className="mb-2 text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded inline-block">
                      对象类型
                    </div>
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {JSON.stringify(response, null, 2)}
                    </pre>
                  </div>
                );
              } else {
                // 其他类型，转换为字符串显示
                return (
                  <div>
                    <div className="mb-2 text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded inline-block">
                      未知类型
                    </div>
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {String(response)}
                    </pre>
                  </div>
                );
              }
            })()}
          </div>
        </div>
      )}

      {/* 调用历史 */}
      {showHistory && (
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-md font-medium text-gray-900">调用历史</h4>
            <div className="space-x-2">
              <button
                onClick={exportHistory}
                disabled={callHistory.length === 0}
                className="px-3 py-1 text-sm bg-green-500 hover:bg-green-600 disabled:bg-gray-300 text-white rounded transition-colors"
              >
                导出
              </button>
              <button
                onClick={clearHistory}
                disabled={callHistory.length === 0}
                className="px-3 py-1 text-sm bg-red-500 hover:bg-red-600 disabled:bg-gray-300 text-white rounded transition-colors"
              >
                清空
              </button>
            </div>
          </div>
          
          {callHistory.length === 0 ? (
            <p className="text-gray-500 text-center py-4">暂无调用记录</p>
          ) : (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {callHistory.map((record) => (
                <div key={record.id} className="border border-gray-200 rounded">
                  <div
                    className="p-3 cursor-pointer hover:bg-gray-50 flex items-center justify-between"
                    onClick={() => setExpandedRecord(expandedRecord === record.id ? null : record.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <span className={`w-2 h-2 rounded-full ${record.success ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      <span className="text-sm font-medium">{record.method}</span>
                      <span className="text-sm text-gray-600">{new Date(record.timestamp).toLocaleString()}</span>
                      <span className="text-sm text-gray-500">{record.duration}ms</span>
                      <span className={`text-sm ${record.success ? 'text-green-600' : 'text-red-600'}`}>
                        {record.status || 'ERROR'}
                      </span>
                    </div>
                    <span className="text-xs text-gray-400">
                      {expandedRecord === record.id ? '收起' : '展开'}
                    </span>
                  </div>
                  
                  {expandedRecord === record.id && (
                    <div className="px-3 pb-3 border-t border-gray-200 bg-gray-50">
                      <div className="mt-2 space-y-2 text-sm">
                        <div>
                          <strong>URL:</strong> <code className="bg-gray-100 px-1 rounded">{record.url}</code>
                        </div>
                        <div>
                          <strong>参数:</strong>
                          <pre className="mt-1 text-xs bg-white p-2 rounded border overflow-auto">
                            {JSON.stringify(record.params, null, 2)}
                          </pre>
                        </div>
                        <div>
                          <strong>响应:</strong>
                          <pre className="mt-1 text-xs bg-white p-2 rounded border max-h-32 overflow-auto">
                            {JSON.stringify(record.response, null, 2)}
                          </pre>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ApiCaller;

// 导出类型定义供其他组件使用
export type { ApiDefinition, ApiParam, ApiCallRecord };