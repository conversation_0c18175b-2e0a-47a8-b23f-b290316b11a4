'use client';
import "./recognition.css"; 
import { observer } from 'mobx-react-lite';
import Sidebar from './layout/Sidebar';
import InfoSection from './layout/InfoSection';
import FileUpload from './features/FileUpload';
import LoadingSpinner from './ui/LoadingSpinner';
import ImageResultsContainer from './features/ImageRecognition/ImageResultsContainer';
import EventDescriptionModal from './features/EventDescription/EventDescriptionModal';
import ConfigModal from './features/Config/ConfigModal';
import ExportModal from './features/Export/ExportModal';  
import { StateProvider } from './StateContext'


function Recognition() { 
  return (
    <StateProvider>
      <div className="page-container">
        <Sidebar /> 
        <div className="container">
          <div className="main-panel">
            <InfoSection />
            <FileUpload />
          </div> 
          <ImageResultsContainer /> 
        </div>
        <LoadingSpinner />
        <EventDescriptionModal />
        <ConfigModal />
        <ExportModal />
      </div>
    </StateProvider>
  );
}

export default observer(Recognition);
