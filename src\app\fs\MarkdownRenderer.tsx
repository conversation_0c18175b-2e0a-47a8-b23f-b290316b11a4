import React, { useState, useEffect, useCallback } from 'react';
import { fileSystemAPI } from './api';
import { parse } from 'marked';
import type { DIR } from '@/utils/fs';
import { message } from '@/utils/message'; // Import the new message utility
import Editor from '@/app/fs/ClientEditor';
import './md.css';
 
interface MarkdownRendererProps {
  content: string;
  fileName: string;
  filePath: string;
  dirType: DIR;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content: _content,
  fileName,
  filePath,
  dirType,
}) => {
  const [showResolveButton, setShowResolveButton] = useState(false);
  const [loading, setLoading] = useState(false);
  const [resolving, setResolving] = useState(false);
  const [content, setContent] = useState(_content);
  const [metaData, setMetaData] = useState<any>(null);
  const [showResolverInput, setShowResolverInput] = useState(false);
  const [resolver, setResolver] = useState('');

  const isEditable = (fileName === 'report.md' && dirType === 'report') || fileName.indexOf('edit_') !== -1; 

  // 加载meta.json数据
  useEffect(() => {
    const loadMetaData = async () => {
      if (fileName === 'report.md' && dirType === 'report') {
        try {
          const metaContent = await fileSystemAPI.getFileContent(dirType, filePath + '/meta.json');
          const meta = JSON.parse(metaContent);
          setMetaData(meta);

          setShowResolveButton(fileName === 'report.md' && dirType === 'report' && meta?.status === 'OPEN');
        } catch (error) {
          console.error('加载meta.json失败:', error);
          // 如果meta.json不存在或加载失败，不显示解决按钮
          setMetaData(null);
        }
      }
    };

    loadMetaData();
  }, [fileName, dirType, filePath]);

  const handleSave = useCallback(async () => {
    if (!isEditable ) return;
    setLoading(true);

    try {
      await fileSystemAPI.uploadFile(dirType, filePath + '/' + fileName, content);

      message.success('保存成功!');
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败!');
    } finally {
      setLoading(false);
    }
  }, [dirType, filePath, setLoading, isEditable, content, fileName]);

  const handleResolve = useCallback(async () => {
    if (!showResolveButton) return;

    // 显示输入框让用户输入处理人
    setShowResolverInput(true);
  }, [showResolveButton]);

  const handleConfirmResolve = useCallback(async () => {
    if (!resolver.trim()) {
      message.error('请输入处理人姓名');
      return;
    }

    setResolving(true);
    setShowResolverInput(false);

    try {
      const response = await fetch('api/bug-report/resolve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filePath,
          dirType,
          resolver: resolver.trim()
        })
      });

      const result = await response.json();

      if (response.ok) {
        message.success('问题已标记为解决，并已发送群消息!');
        setShowResolveButton(false);
        setResolver('');
        // 更新本地meta数据状态
        setMetaData((prev:any) => prev ? { ...prev, status: 'CLOSE', resolver: resolver.trim() } : null);
      } else {
        message.error(`操作失败: ${result.error}`);
      }
    } catch (error) {
      console.error('问题解决失败:', error);
      message.error('操作失败，请稍后重试');
    } finally {
      setResolving(false);
    }
  }, [filePath, dirType, resolver]);

  const handleCancelResolve = useCallback(() => {
    setShowResolverInput(false);
    setResolver('');
  }, []);
  

  return (
    <div>
      {!isEditable && (
        <div className="markdown-body" dangerouslySetInnerHTML={{__html: parse(content)}} >
        </div>
      )}
      {isEditable && (
        <Editor
          onChange={(event, editor) => setContent(editor.getData())}
          data={content}
          disabled={loading}
        />
      )}
      {isEditable && (
        <div className="mt-2 flex gap-2">
          <button
            type="submit"
            onClick={handleSave}
            className={`bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={loading}
          >
            {loading ? '保存中...' : '保存'}
          </button>

          {showResolveButton && !showResolverInput && (
            <button
              type="button"
              onClick={handleResolve}
              className={`bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded ${resolving ? 'opacity-50 cursor-not-allowed' : ''}`}
              disabled={resolving}
            >
              {resolving ? '处理中...' : '问题解决'}
            </button>
          )}

          {showResolverInput && (
            <div className="flex items-center gap-2">
              <input
                type="text"
                value={resolver}
                onChange={(e) => setResolver(e.target.value)}
                placeholder="请输入处理人姓名"
                className="border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleConfirmResolve();
                  }
                }}
                autoFocus
              />
              <button
                type="button"
                onClick={handleConfirmResolve}
                className={`bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm ${resolving ? 'opacity-50 cursor-not-allowed' : ''}`}
                disabled={resolving}
              >
                {resolving ? '处理中...' : '确认'}
              </button>
              <button
                type="button"
                onClick={handleCancelResolve}
                className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded text-sm"
                disabled={resolving}
              >
                取消
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MarkdownRenderer;