'use client'
import React, { useState, useEffect } from 'react';
import { useStateStore } from '../../StateContext';
import { observer } from 'mobx-react-lite'; 

interface Label {
  id: string;
  text: string;
  color: string;
  type: string;
  confidence?: number;
}

interface LabelPanelProps {
  isOpen?: boolean;
  onClose: () => void;
  initialLabels?: Label[];
  onLabelsChange?: (labels: Label[]) => void;
}

const LabelPanel: React.FC<LabelPanelProps> = ({
  isOpen = true,
  onClose,
  initialLabels,
  onLabelsChange
}) => {
  const appStore = useStateStore();  
   
  if (!isOpen) return null;
       

  return (
    <div className="modal-overlay">
      <div className="modal-content label-panel">
        <div className="modal-header">
          <h5 className="modal-title">标签管理</h5>
          <button 
            type="button" 
            className="close-button"
            onClick={onClose}
          >
            ×
          </button>
        </div>
        
        <div className="modal-body">
          <div className="label-controls">
            <div className="filter-section"> 
               
            </div>
             
          </div> 
        </div>
        
        <div className="modal-footer">
          <button 
            type="button" 
            className="btn btn-secondary"
            onClick={onClose}
          >
            关闭
          </button> 
        </div>
      </div>
    </div>
  );
};

 

export default observer(LabelPanel);