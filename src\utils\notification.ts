
// 显示浏览器通知
export const showNotification = (title: string, body: string = '') => {
  // 检查通知权限
  if (Notification.permission === 'granted') {
    // 获取当前时间
    const now = new Date();
    const timeString = now.toLocaleString();
    
    // 创建通知内容
    const notificationContent = `${timeString}\n${body}`;
    
    // 显示通知
    new Notification(title, {
      body: notificationContent,
      icon: 'https://www.pharmaron.cn/images/logo.png'
    });
  } else if (Notification.permission !== 'denied') {
    // 如果尚未获得权限且未被拒绝，请求通知权限
    Notification.requestPermission().then(permission => {
      if (permission === 'granted') {
        showNotification(title, body);
      }
    });
  }
}

export const requestPermission = () => {
  if (Notification.permission !== 'granted' && Notification.permission !== 'denied') {
    Notification.requestPermission();
  }
  if (Notification.permission === 'denied') {
    alert('请在浏览器设置中允许通知权限');
  }
};