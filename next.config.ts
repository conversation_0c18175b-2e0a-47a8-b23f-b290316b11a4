import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  basePath: process.env.NODE_ENV === 'production' ? '/pv-manus-front': '',
  assetPrefix: process.env.NODE_ENV === 'production' ? '/pv-manus-front': '',
  output: 'standalone',
  // 禁用public目录的缓存
  async headers() {
    return [
      {
        // 匹配静态资源文件
        source: '/:path*.(jpg|jpeg|png|gif|svg|ico|webp|mp4|webm|woff|woff2|ttf|eot|pdf|json|html|js|css)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, no-cache, must-revalidate, proxy-revalidate',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
    ];
  },
  async rewrites() {
    if (process.env.NODE_ENV !== 'production') {
      return [
        {
          source: '/api/:path*',
          destination: 'https://copilot-test.pharmaronclinical.com/api/:path*',
        },
        {
          source: '/kb-server/:path*',
          destination: 'https://copilot-test.pharmaronclinical.com/kb-server/:path*',
        },
      ];
    }
    return [];
  },
};

export default nextConfig;
