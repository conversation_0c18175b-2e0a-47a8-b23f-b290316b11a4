import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { BiErrorCircle, BiSearch, BiChevronLeft, BiChevronRight, BiX } from 'react-icons/bi';
import { FaSort, FaSortUp, FaSortDown } from 'react-icons/fa';
import { HiOutlineRefresh } from 'react-icons/hi';
import { debounce } from 'lodash';
import ApiCaller from './api-caller-extension';

// 表头定义
interface TableHeader {
  name: string; // 显示名称（中文）
  key: string;  // 字段键名
}

// 分页信息
interface PaginationInfo {
  total: number;
  page: number;
  pageSize: number;
}

// API返回的标准数据结构
interface ApiResponse {
  data: {
    headers: TableHeader[];
    rows: Record<string, any>[];
    pagination: PaginationInfo;
    sortBy?: string; // 格式：'key1:ASC'
  };
  code: number; // 10000 为成功
  message: string; // 错误信息
}

// 定义API表格的数据类型
interface ApiTableData {
  headers: TableHeader[];
  rows: Record<string, any>[];
  pagination: PaginationInfo;
  sortBy?: string;
  loading?: boolean;
  error?: string;
}

// 排序方向
type SortDirection = 'ASC' | 'DESC' | null;

// 按钮定义接口
interface ApiTableButton {
  id: string;
  text: string;
  description?: string;
  type: 'header' | 'row';
  variant: 'primary' | 'secondary' | 'danger' | 'success';
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  url: string;
  rowDataMapping?: Record<string, string>;
  params?: any[];
}

// API表格组件
interface EnhancedApiTableProps {
  apiUrl: string;
  className?: string;
  defaultPageSize?: number;
  showSearch?: boolean;
  showPagination?: boolean;
  searchPlaceholder?: string;
  buttons?: ApiTableButton[];
}

const EnhancedApiTable: React.FC<EnhancedApiTableProps> = ({
  apiUrl,
  className = '',
  defaultPageSize = 10,
  showSearch = true,
  showPagination = true,
  searchPlaceholder = '搜索...',
  buttons = []
}) => {
  const [data, setData] = useState<ApiTableData>({
    headers: [],
    rows: [],
    pagination: { total: 0, page: 1, pageSize: defaultPageSize },
    sortBy: undefined,
    loading: true,
    error: undefined
  });
  
  // 搜索、排序、分页状态
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPageSize);
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);
  
  // 模态框状态
  const [showModal, setShowModal] = useState(false);
  const [currentApiDefinition, setCurrentApiDefinition] = useState<any>(null);
  const [needsRefresh, setNeedsRefresh] = useState(false);
  
  // 构建 API URL 参数
  const buildApiUrl = useCallback(() => {
    const url = new URL(apiUrl, window.location.href);
    const params = new URLSearchParams();
    
    // 添加分页参数
    if (showPagination) {
      params.append('page', currentPage.toString());
      params.append('pageSize', pageSize.toString());
    }
    
    // 添加搜索参数
    if (showSearch && searchTerm.trim()) {
      params.append('search', searchTerm.trim());
    }
    
    // 添加排序参数
    if (sortColumn && sortDirection) {
      params.append('sortBy', `${sortColumn}:${sortDirection}`);
    }
    
    // 合并现有的 URL 参数
    const existingParams = new URLSearchParams(url.search);
    existingParams.forEach((value, key) => {
      if (!params.has(key)) {
        params.append(key, value);
      }
    });
    
    url.search = params.toString();
    return url.toString();
  }, [apiUrl, currentPage, pageSize, searchTerm, sortColumn, sortDirection, showSearch, showPagination]);

  // 获取数据
  const fetchData = useCallback(async (isInitialLoad = false) => {
    try {
      setData(prev => ({ ...prev, loading: true, error: undefined }));

      const finalUrl = buildApiUrl();
      const response = await fetch(finalUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse = await response.json();

      // 检查业务状态码
      if (result.code !== 10000) {
        throw new Error(result.message || '请求失败');
      }

      // 处理标准数据格式
      const { headers, rows, pagination, sortBy } = result.data;

      // 验证必要字段
      if (!headers || !Array.isArray(headers)) {
        throw new Error('数据格式错误：缺少 headers 字段');
      }

      if (!rows || !Array.isArray(rows)) {
        throw new Error('数据格式错误：缺少 rows 字段');
      }

      if (!pagination || typeof pagination.total !== 'number') {
        throw new Error('数据格式错误：缺少 pagination 字段');
      }

      // 更新当前页码（以服务端返回为准）
      setCurrentPage(pagination.page);
      setPageSize(pagination.pageSize);

      // 更新排序状态（以服务端返回为准）
      if (sortBy) {
        const [column, direction] = sortBy.split(':');
        setSortColumn(column);
        setSortDirection(direction as SortDirection);
      } else {
        setSortColumn(null);
        setSortDirection(null);
      }

      // 根据是否为初始加载决定更新策略
      setData(prev => {
        if (isInitialLoad || prev.headers.length === 0) {
          // 初始加载或表头为空时，更新所有数据
          return {
            headers,
            rows,
            pagination,
            sortBy,
            loading: false,
            error: undefined
          };
        } else {
          // 非初始加载时，只更新内容和分页，保持表头不变
          return {
            ...prev,
            rows,
            pagination,
            sortBy,
            loading: false,
            error: undefined
          };
        }
      });
    } catch (error) {
      console.error('获取API数据失败:', error);
      setData(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '未知错误'
      }));
    }
  }, [buildApiUrl]);

  // 初始加载和依赖更新时重新获取数据
  useEffect(() => {
    fetchData(true); // 初始加载时传入 true
  }, [fetchData]);

  // 创建防抖搜索函数
  const debouncedSearch = useMemo(
    () => debounce((value: string) => {
      setSearchTerm(value);
      setCurrentPage(1); // 搜索时重置到第一页
    }, 500), // 500ms 防抖延迟
    []
  );

  // 搜索处理
  const handleSearch = useCallback((value: string) => {
    debouncedSearch(value);
  }, [debouncedSearch]);

  // 清理防抖函数
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  // 排序处理
  const handleSort = useCallback((columnKey: string) => {
    if (sortColumn === columnKey) {
      // 同一列：ASC -> DESC -> null
      if (sortDirection === 'ASC') {
        setSortDirection('DESC');
      } else if (sortDirection === 'DESC') {
        setSortDirection(null);
        setSortColumn(null);
      } else {
        setSortDirection('ASC');
      }
    } else {
      // 不同列：设置为 ASC
      setSortColumn(columnKey);
      setSortDirection('ASC');
    }
    setCurrentPage(1); // 排序时重置到第一页
  }, [sortColumn, sortDirection]);

  // 分页处理
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const handlePageSizeChange = useCallback((size: number) => {
    setPageSize(size);
    setCurrentPage(1); // 改变页面大小时重置到第一页
  }, []);

  // 刷新数据
  const handleRefresh = useCallback(() => {
    fetchData();
  }, [fetchData]);

  // 计算分页信息
  const totalPages = Math.ceil(data.pagination.total / data.pagination.pageSize);
  const startRecord = (data.pagination.page - 1) * data.pagination.pageSize + 1;
  const endRecord = Math.min(data.pagination.page * data.pagination.pageSize, data.pagination.total);

  // 渲染排序图标
  const renderSortIcon = (columnKey: string) => {
    if (sortColumn !== columnKey) {
      return <FaSort className="ml-1 opacity-30" />;
    }
    if (sortDirection === 'ASC') {
      return <FaSortUp className="ml-1 text-blue-600" />;
    }
    if (sortDirection === 'DESC') {
      return <FaSortDown className="ml-1 text-blue-600" />;
    }
    return <FaSort className="ml-1 opacity-30" />;
  };

  

  if (data.error) {
    return (
      <div className={`enhanced-api-table-error ${className}`}>
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <BiErrorCircle className="text-red-500 text-xl mr-2" />
              <div>
                <p className="text-red-800 font-medium">数据加载失败</p>
                <p className="text-red-600 text-sm mt-1">{data.error}</p>
                <p className="text-red-600 text-sm">API地址: {buildApiUrl()}</p>
              </div>
            </div>
            <button
              onClick={handleRefresh}
              className="flex items-center px-3 py-1 text-sm bg-red-100 hover:bg-red-200 text-red-700 rounded transition-colors"
            >
              <HiOutlineRefresh className="mr-1" />
              重试
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (data.headers.length === 0) {
    return (
      <div className={`enhanced-api-table-empty ${className}`}>
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800">没有可显示的数据</p>
        </div>
      </div>
    );
  }

  // 过滤表头按钮和行按钮
  const headerButtons = buttons.filter(btn => btn.type === 'header');
  const rowButtons = buttons.filter(btn => btn.type === 'row');

  // 获取按钮样式
  const getButtonStyle = (variant: string) => {
    switch (variant) {
      case 'primary':
        return 'bg-blue-600 hover:bg-blue-700 text-white';
      case 'secondary':
        return 'bg-gray-500 hover:bg-gray-600 text-white';
      case 'danger':
        return 'bg-red-600 hover:bg-red-700 text-white';
      case 'success':
        return 'bg-green-600 hover:bg-green-700 text-white';
      default:
        return 'bg-gray-600 hover:bg-gray-700 text-white';
    }
  };

  // 处理按钮点击
  const handleButtonClick = (button: ApiTableButton, rowData?: any) => {
    // 构建API定义
    const apiDefinition = {
      id: button.id,
      name: button.text,
      description: button.description || `${button.text} - ${button.method} ${button.url}`,
      method: button.method,
      url: button.url,
      headers: {},
      params: button.params || []
    }; 

    // 如果有行数据映射，预填充参数
    if (rowData && button.rowDataMapping) {
      const prefilledParams: Record<string, any> = {};
      Object.entries(button.rowDataMapping).forEach(([paramName, columnName]) => {
        if (rowData[columnName] !== undefined) {
          prefilledParams[paramName] = rowData[columnName];
        }
      });
      
      // 更新参数默认值
      apiDefinition.params = apiDefinition.params.map((param: any) => ({
        ...param,
        defaultValue: prefilledParams[param.name] !== undefined
          ? prefilledParams[param.name]
          : param.defaultValue
      }));
    }

    setCurrentApiDefinition(apiDefinition);
    setShowModal(true);
  };

  // 关闭模态框的统一处理函数
  const closeModal = () => {
    setShowModal(false);
    // 关闭模态框时，如果需要刷新则刷新表格数据
    if (needsRefresh) {
      fetchData();
      setNeedsRefresh(false);
    }
  }; 
  return (
    <div className={`enhanced-api-table ${className}`}>
      {/* 搜索栏和表头按钮 */}
      <div className="mb-4 flex items-center justify-between space-x-2">
        {showSearch && (
          <div className="relative flex-1">
            <BiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder={searchPlaceholder}
              defaultValue={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        )}
        
        <div className="flex items-center space-x-2">
          {/* 表头按钮 */}
          {headerButtons.map((button) => (
            <button
              key={button.id}
              onClick={() => handleButtonClick(button)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${getButtonStyle(button.variant)}`}
              title={button.description}
            >
              {button.text}
            </button>
          ))}
          
          {/* 刷新按钮 */}
          <button
            onClick={handleRefresh}
            className="flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
            title="刷新数据"
          >
            <HiOutlineRefresh />
          </button>
        </div>
      </div>
      {data.loading && (
        <div className={`enhanced-api-table-loading ${className}`}>
          <div className="flex items-center justify-center p-8 bg-gray-50 rounded-lg">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span className="text-gray-600">加载数据中...</span>
          </div>
        </div>
      )}

      {!data.loading && (<>
      {/* 表格 */}
      <div className="overflow-x-auto">
        <table className="min-w-full border-collapse border border-gray-300 rounded-lg overflow-hidden">
          <thead className="bg-gray-50">
            <tr>
              {data.headers.map((header, index) => (
                <th
                  key={index}
                  className="border border-gray-300 px-4 py-2 text-left font-semibold text-gray-900 cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort(header.key)}
                >
                  <div className="flex items-center">
                    {header.name}
                    {renderSortIcon(header.key)}
                  </div>
                </th>
              ))}
              {/* 操作列 */}
              {rowButtons.length > 0 && (
                <th className="border border-gray-300 px-4 py-2 text-left font-semibold text-gray-900">
                  操作
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {data.rows.map((row, rowIndex) => {
              return (
                <tr
                  key={rowIndex}
                  className={rowIndex % 2 === 0 ? 'bg-white hover:bg-gray-50' : 'bg-gray-50 hover:bg-gray-100'}
                >
                  {data.headers.map((header, cellIndex) => (
                    <td
                      key={cellIndex}
                      className="border border-gray-300 px-4 py-2 text-gray-700"
                    >
                      {String(row[header.key] || '')}
                    </td>
                  ))}
                  {/* 操作按钮列 */}
                  {rowButtons.length > 0 && (
                    <td className="border border-gray-300 px-4 py-2">
                      <div className="flex space-x-2">
                        {rowButtons.map((button) => (
                          <button
                            key={button.id}
                            onClick={() => handleButtonClick(button, row)}
                            className={`px-2 py-1 rounded text-xs font-medium transition-colors ${getButtonStyle(button.variant)}`}
                            title={button.description}
                          >
                            {button.text}
                          </button>
                        ))}
                      </div>
                    </td>
                  )}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* 分页控件 */}
      {showPagination && (
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <span>每页显示:</span>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              className="border border-gray-300 rounded px-2 py-1"
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
            <span>
              显示 {startRecord}-{endRecord} 条，共 {data.pagination.total} 条记录
            </span>
          </div>
          
          <div className="flex items-center space-x-1">
            <button
              onClick={() => handlePageChange(data.pagination.page - 1)}
              disabled={data.pagination.page === 1}
              className="flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed rounded transition-colors"
            >
              <BiChevronLeft />
              上一页
            </button>
            
            {/* 页码按钮 */}
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum;
              const currentPageNum = data.pagination.page;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPageNum <= 3) {
                pageNum = i + 1;
              } else if (currentPageNum >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPageNum - 2 + i;
              }
              
              return (
                <button
                  key={pageNum}
                  onClick={() => handlePageChange(pageNum)}
                  className={`px-3 py-1 rounded transition-colors ${
                    data.pagination.page === pageNum
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}
            
            <button
              onClick={() => handlePageChange(data.pagination.page + 1)}
              disabled={data.pagination.page === totalPages}
              className="flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed rounded transition-colors"
            >
              下一页
              <BiChevronRight />
            </button>
          </div>
        </div>
      )}

      {/* 数据源信息 */}
      <div className="mt-2 text-xs text-gray-500 text-right">
        数据来源: {apiUrl} | 当前显示 {data.rows.length} 条记录
        {searchTerm && ` | 搜索: "${searchTerm}"`}
        {data.sortBy && ` | 排序: ${data.sortBy}`}
      </div>
      </>)}

      {/* API调用模态框 */}
      {showModal && currentApiDefinition && (
        <div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
          onClick={(e) => {
            // 点击背景关闭模态框
            if (e.target === e.currentTarget) {
              closeModal();
            }
          }}
        >
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto mx-4">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div></div>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <BiX size={24} />
              </button>
            </div>
            <div className="p-0">
              <ApiCaller
                definition={currentApiDefinition}
                onSuccess={() => {
                  // API调用成功，标记需要刷新
                  setNeedsRefresh(true);
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedApiTable;