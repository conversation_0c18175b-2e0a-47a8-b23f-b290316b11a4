{
  "compilerOptions": {
    "target": "es2015",
    "lib": [
      "dom",
      "es5",
      "es2015"
    ],
    "jsx": "preserve",
    "module": "commonjs",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "typeRoots": [
      "./node_modules/@types",
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "src/*"
      ]
    },
    "allowJs": true,
    "noEmit": true,
    "incremental": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": [
    "src",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules"
  ]
}
