'use client'
import React from 'react'; 
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { ClassicEditor,
  Essentials,
  Autoformat,
  BlockQuote,
  Bold,
  CloudServices,
  Code,
  CodeBlock,
  Heading,
  HorizontalLine,
  Image,
  ImageToolbar,
  ImageUpload,
  Base64UploadAdapter,
  Italic,
  Link,
  List,
  Markdown,
  Mention,
  Paragraph,
  SpecialCharacters,
  SpecialCharactersEssentials,
  Strikethrough,
  Table,
  TableToolbar,
  TextTransformation,
  TodoList,
  ButtonView } from 'ckeditor5';
import { FormatPainter } from 'ckeditor5-premium-features';

import 'ckeditor5/ckeditor5.css';
import 'ckeditor5-premium-features/ckeditor5-premium-features.css';
import "./editor.css";
import AddFeedbackPlugin from './plugins/AddFeedbackPlugin';

class MyUploadAdapter {
  loader: any;
  constructor(loader: any) {
    this.loader = loader;
  }

  upload() {
    return this.loader.file.then(
      (file: any) =>
        new Promise((resolve, reject) => {
          const toBase64 = (file: any) =>
            new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.readAsDataURL(file);
              reader.onload = () => resolve(reader.result);
              reader.onerror = (error) => reject(error);
            });
          const base64_image = toBase64(file).then((data) => {
            return resolve({
              default: data,
            });
          });
          this.loader.uploaded = true;
          return base64_image;
        })
    );
  }
}

function MyCustomUploadAdapterPlugin(editor: any) {
  editor.plugins.get("FileRepository").createUploadAdapter = (loader: any) => {
    return new MyUploadAdapter(loader);
  };
}

class MyEditor extends ClassicEditor {
  static builtinPlugins = [
    Autoformat,
    BlockQuote,
    Bold,
    CloudServices,
    Code,
    CodeBlock,
    Essentials,
    Heading,
    HorizontalLine,
    Image,
    ImageToolbar,
    ImageUpload,
    Base64UploadAdapter,
    Italic,
    Link,
    List,
    Markdown,
    Mention,
    Paragraph,
    SpecialCharacters,
    SpecialCharactersEssentials,
    Strikethrough,
    Table,
    TableToolbar,
    TextTransformation,
    TodoList,
    AddFeedbackPlugin,
  ];

  static defaultConfig = {
    language: "zh-cn",
    toolbar: [
      "undo",
      "redo",
      "|",
      "heading",
      "|",
      "bold",
      "italic",
      "strikethrough",
      "code",
      "|",
      "bulletedList",
      "numberedList",
      "todoList",
      "|",
      "link",
      "uploadImage",
      "insertTable",
      "blockQuote",
      "codeBlock",
      "horizontalLine",
      "specialCharacters",
      "|",
      "addFeedback",
    ],
    codeBlock: {
      languages: [
        { language: "css", label: "CSS" },
        { language: "html", label: "HTML" },
        { language: "javascript", label: "JavaScript" },
        { language: "python", label: "Python" },
      ],
    },
    heading: {
      options: [
        {
          model: "paragraph" as const,
          title: "Paragraph",
          class: "ck-heading_paragraph",
        },
        {
          model: "heading1"as const,
          view: "h1",
          title: "Heading 1",
          class: "ck-heading_heading1",
        },
        {
          model: "heading2" as const,
          view: "h2",
          title: "Heading 2",
          class: "ck-heading_heading2",
        },
        {
          model: "heading3" as const,
          view: "h3",
          title: "Heading 3",
          class: "ck-heading_heading3",
        },
        {
          model: "heading4" as const,
          view: "h4",
          title: "Heading 4",
          class: "ck-heading_heading4",
        },
        {
          model: "heading5" as const,
          view: "h5",
          title: "Heading 5",
          class: "ck-heading_heading5",
        },
        {
          model: "heading6" as const,
          view: "h6",
          title: "Heading 6",
          class: "ck-heading_heading6",
        },
      ],
    },
    image: {
      toolbar: ["imageTextAlternative"],
    },
    table: {
      contentToolbar: ["tableColumn", "tableRow", "mergeTableCells"],
    },
  };
} 
export type EditorWarpProps = Omit<React.ComponentProps<typeof CKEditor>, 'editor'>;

const CustomEditor:React.FC<EditorWarpProps> = (props) => {
  return (
      <CKEditor
        {...props}
          editor={ MyEditor }
          config={ {
            licenseKey: "GPL",
            extraPlugins: [MyCustomUploadAdapterPlugin],
          }}
      />
  );
}

export default CustomEditor; 