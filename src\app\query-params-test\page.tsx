'use client';
import React from 'react';
import MarkdownPlus from '../MarkdownPlus';

const QueryParamsTestPage = () => {
  const markdownContent = `
# API表格动态查询参数功能测试

这个页面用于测试API表格组件的动态查询参数功能。

## 功能说明

- **动态查询参数**: 支持从API接口加载查询参数选项
- **实时过滤**: 选择查询参数后实时过滤表格数据
- **多参数组合**: 支持多个查询参数同时使用
- **默认值设置**: 支持设置查询参数的默认值

## 测试用例1：用户数据表格（带状态和角色过滤）

下面的表格包含状态和角色两个查询参数：

\`\`\`api-table:/api/test-data?type=users
{
  "pageSize": 10,
  "showSearch": true,
  "showPagination": true,
  "searchPlaceholder": "搜索用户...",
  "queryParams": [
    {
      "name": "status",
      "label": "状态",
      "type": "dict",
      "dictUrl": "/api/query-params?type=status"
    },
    {
      "name": "role",
      "label": "角色",
      "type": "dict",
      "dictUrl": "/api/query-params?type=role"
    }
  ]
}
\`\`\`

## 测试用例2：用户数据表格（带部门过滤和默认值）

下面的表格包含部门查询参数，并设置了默认值：

\`\`\`api-table:/api/test-data?type=users
{
  "pageSize": 5,
  "showSearch": true,
  "showPagination": true,
  "searchPlaceholder": "搜索用户...",
  "queryParams": [
    {
      "name": "department",
      "label": "部门",
      "type": "dict",
      "dictUrl": "/api/query-params?type=department",
      "defaultValue": "技术部"
    },
    {
      "name": "status",
      "label": "状态",
      "type": "dict",
      "dictUrl": "/api/query-params?type=status"
    }
  ]
}
\`\`\`

## 测试用例3：产品数据表格（带分类过滤）

下面的表格包含产品分类查询参数：

\`\`\`api-table:/api/test-data?type=products
{
  "pageSize": 8,
  "showSearch": true,
  "showPagination": true,
  "searchPlaceholder": "搜索产品...",
  "queryParams": [
    {
      "name": "category",
      "label": "分类",
      "type": "dict",
      "dictUrl": "/api/query-params?type=category"
    }
  ]
}
\`\`\`

## 测试步骤

1. **查看查询参数加载**: 观察搜索框右侧的查询参数下拉框是否正确加载选项
2. **测试默认值**: 检查设置了默认值的查询参数是否正确显示默认选项
3. **测试过滤功能**: 选择不同的查询参数值，观察表格数据是否正确过滤
4. **测试组合过滤**: 同时使用多个查询参数，测试组合过滤效果
5. **测试搜索结合**: 在使用查询参数的同时使用搜索功能，测试两者结合效果
6. **测试分页**: 在过滤状态下测试分页功能是否正常
7. **测试排序**: 在过滤状态下测试排序功能是否正常

## 预期行为

- 查询参数选项应该从对应的API接口正确加载
- 选择查询参数后，表格数据应该实时过滤
- 多个查询参数应该支持组合过滤（AND逻辑）
- 查询参数过滤应该与搜索功能正确结合
- 过滤后的数据应该正确支持分页和排序
- 查询参数变化时应该重置到第一页

## API接口说明

### 查询参数选项接口
- **URL**: \`/api/query-params?type={type}\`
- **方法**: GET
- **参数**: 
  - \`type\`: 查询参数类型（status, role, department, category等）
- **返回格式**: 
  \`\`\`json
  {
    "code": 10000,
    "message": "获取成功",
    "data": ["选项1", "选项2", "选项3"]
  }
  \`\`\`

### 数据查询接口
- **URL**: \`/api/test-data?type=users&status={status}&role={role}&department={department}\`
- **方法**: GET
- **参数**: 
  - 标准参数：\`type\`, \`search\`, \`sortBy\`, \`page\`, \`pageSize\`
  - 查询参数：根据配置动态添加（如\`status\`, \`role\`, \`department\`等）
`;

  return (
    <div className="container mx-auto p-6">
      <MarkdownPlus content={markdownContent} />
    </div>
  );
};

export default QueryParamsTestPage;
