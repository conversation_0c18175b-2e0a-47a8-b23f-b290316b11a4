"use client";
import React from "react";
import { useStateStore } from "../StateContext";
import { observer } from "mobx-react-lite";

  
const formatTime = (date: Date | string | null) => {
  if (!date) return "--:--:--";
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return dateObj.toTimeString().split(" ")[0];
};

 
const LoadingSpinner: React.FC = () => {
  const appStore = useStateStore();

  if (!appStore.loading.show) {
    return null;
  } 
  
  const calculateTotalTime = () => {
    if (!appStore.startTime || !appStore.endTime) return "--";
    const start =
      typeof appStore.startTime === "string"
        ? new Date(appStore.startTime)
        : appStore.startTime;
    const end =
      typeof appStore.endTime === "string"
        ? new Date(appStore.endTime)
        : appStore.endTime;
    return ((end.getTime() - start.getTime()) / 1000).toFixed(2);
  };

  const startTime = formatTime(appStore.startTime);
  const endTime = formatTime(appStore.endTime);
  const totalTime = calculateTotalTime();

  const spinnerStyle: React.CSSProperties = {
    display: "flex",
    backgroundColor: appStore.loading.error
      ? "#D48806"
      : appStore.loading.show
      ? "#716DCC"
      : "#3A8176",
    animation: !appStore.loading.show ? "flash 1s linear infinite" : "none",
    pointerEvents: !appStore.loading.show ? "auto" : "none",
  };

  const handleSpinnerClick = () => {
    appStore.hideLoading();
  };

  return (
    <div
      id="loading-spinner"
      className="loading-spinner"
      style={spinnerStyle}
      onClick={handleSpinnerClick}
    >
      <div
        className={`spinner-icon ${appStore.loading.show ? "rotate" : ""}`}
      ></div>
      <div className="loading-info">
        <p id="currentNode">{appStore.loading.name || "初始化中..."}</p>
        <p id="loadingMessage">
          {appStore.loading.show
            ? "运行中，请勿点击 loading 框"
            : "运行完成，请确认结果后，点击 loading 框来结束当前流程"}
        </p>
        <p id="startTime">开始时间: {startTime}</p>
        <p id="endTime">结束时间: {endTime}</p>
        <p id="totalTime">总耗时: {totalTime} 秒</p>
        <div
          id="progress-container"
          style={{ display: appStore.loading.error ? "none" : "block" }}
        >
          <progress
            id="loadingProgress"
            value={appStore.loading.progress || 0}
            max="100"
          ></progress>
          <p id="progressText">{Math.round(appStore.loading.progress || 0)}%</p>
        </div>
      </div>
    </div>
  );
};

export default observer(LoadingSpinner);
