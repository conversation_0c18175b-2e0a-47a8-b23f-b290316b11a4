import MarkdownPlus from '@/app/MarkdownPlus'
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export const metadata = {
  title: "Markdown Plus",
  description: "Markdown Plus",
};

export default function MarkdownPage() {
  const [file, setFile] = useState('');
  useEffect(() => {
    const url = new URLSearchParams(window.location.search);
    const file = url.get('file') || 'demo.md';
    document.title = file.replace('.md', '');
    setFile(file)
  },[])  
  
  return <MarkdownPlus markdownFile={file} />;
}