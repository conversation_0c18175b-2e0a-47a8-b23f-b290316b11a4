# Markdown API 表格扩展语法

这是一个为租户初始化页面开发的 Markdown 扩展功能，允许在 Markdown 文档中直接从 API 加载数据并显示为表格。

## 功能特性

- 🔄 **动态数据加载**：从 REST API 实时获取数据
- 📊 **多种数据格式支持**：支持标准表格格式、对象数组等
- 🎨 **响应式设计**：自动适配不同屏幕尺寸
- ⚡ **加载状态**：显示加载指示器和错误处理
- 📈 **数据统计**：显示记录数量和数据来源

## 语法格式

### 基本语法
```markdown
```api-table:API_URL```
```

### 带选项的语法
```markdown
```api-table:API_URL
option1: value1
option2: value2
```
```

## 支持的选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `className` | string | `'my-4'` | 自定义 CSS 类名 |

## 支持的数据格式

### 1. 标准表格格式
```json
{
  "headers": ["列1", "列2", "列3"],
  "rows": [
    ["值1", "值2", "值3"],
    ["值4", "值5", "值6"]
  ]
}
```

### 2. 包装数据格式
```json
{
  "data": [
    {"name": "张三", "age": 25, "department": "开发部"},
    {"name": "李四", "age": 30, "department": "测试部"}
  ]
}
```

### 3. 直接对象数组
```json
[
  {"name": "张三", "age": 25, "department": "开发部"},
  {"name": "李四", "age": 30, "department": "测试部"}
]
```

## 使用示例

### 显示租户信息
```markdown
## 租户基础信息

```api-table:/api/demo-data?type=tenants```
```

### 显示临床试验数据
```markdown
## 临床试验信息

```api-table:/api/demo-data?type=trials
className: my-6
```
```

### 显示员工信息
```markdown
## 员工列表

```api-table:/api/demo-data?type=object-array```
```

## 错误处理

- **网络错误**：显示错误信息和重试提示
- **数据格式错误**：显示格式不支持的提示
- **空数据**：显示没有数据的提示
- **加载超时**：显示加载超时的提示

## 样式定制

可以通过 `className` 选项添加自定义样式：

```markdown
```api-table:/api/your-endpoint
className: custom-table-style
```
```

然后在 CSS 中定义样式：

```css
.custom-table-style {
  margin: 2rem 0;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.custom-table-style table {
  font-size: 14px;
}
```

## 技术实现

- **框架**：React + TypeScript
- **Markdown 解析**：marked.js
- **状态管理**：React useState + useEffect
- **渲染**：React createRoot (React 18+)
- **样式**：Tailwind CSS

## 文件结构

```
src/app/tenant-init/
├── index.tsx                 # 主组件
├── markdown-extensions.tsx   # API 表格扩展
├── page.tsx                  # Next.js 页面
└── tenant-init.css          # 样式文件
```

## 演示页面

访问以下页面查看功能演示：

- 租户初始化页面：`/tenant-init`
- API 表格演示：`/api-table-demo`

## 注意事项

1. **CORS 政策**：确保 API 端点正确配置 CORS
2. **数据格式**：API 返回的数据必须符合支持的格式之一
3. **性能考虑**：对于大量数据，建议在 API 端实现分页
4. **安全性**：避免在客户端暴露敏感的 API 端点